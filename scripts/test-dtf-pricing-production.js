#!/usr/bin/env node

/**
 * Production-ready DTF Pricing System Test Script
 *
 * This script comprehensively tests the DTF pricing management system
 * including validation, error handling, rate limiting, and quote calculations.
 */

const BASE_URL = 'http://localhost:3000'

// Test configuration
const TEST_CONFIG = {
  isActive: true,
  pricingModel: 'per_square_inch',
  measurementUnit: 'inches',
  currency: 'INR',
  basePricing: {
    minimumOrderValue: 500,
    setupFee: 100,
    rushOrderMultiplier: 1.5,
  },
  sizeTiers: [
    {
      tierName: 'Small',
      dimensions: {
        minWidth: 0.1,
        maxWidth: 5,
        minHeight: 0.1,
        maxHeight: 5,
      },
      pricing: {
        basePrice: 50,
        pricePerUnit: 2,
      },
      isActive: true,
    },
    {
      tierName: 'Medium',
      dimensions: {
        minWidth: 5.1,
        maxWidth: 10,
        minHeight: 5.1,
        maxHeight: 10,
      },
      pricing: {
        basePrice: 100,
        pricePerUnit: 1.5,
      },
      isActive: true,
    },
    {
      tierName: 'Large',
      dimensions: {
        minWidth: 10.1,
        minHeight: 10.1,
      },
      pricing: {
        basePrice: 200,
        pricePerUnit: 1,
      },
      isActive: true,
    },
  ],
  quantityDiscounts: [],
  materialOptions: [],
  additionalServices: [],
  paymentTerms: {
    defaultTerms: 'full_upfront',
    minimumAdvancePercentage: 50,
  },
}

// Test utilities
async function makeRequest(endpoint, options = {}) {
  const url = `${BASE_URL}${endpoint}`
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  })

  const data = await response.json()
  return { response, data }
}

function logTest(testName, success, details = '') {
  const status = success ? '✅ PASS' : '❌ FAIL'
  console.log(`${status} ${testName}${details ? ` - ${details}` : ''}`)
}

function logSection(sectionName) {
  console.log(`\n🔍 ${sectionName}`)
  console.log('='.repeat(50))
}

// Test functions
async function testGetPricingConfig() {
  logSection('Testing GET /api/globals/dtf-pricing')

  try {
    const { response, data } = await makeRequest('/api/globals/dtf-pricing')

    if (response.ok) {
      logTest('GET pricing config', true, `Status: ${response.status}`)
      logTest(
        'Response structure',
        data.success !== undefined,
        `Has success field: ${data.success}`,
      )
      return data.data || data
    } else {
      logTest('GET pricing config', false, `Status: ${response.status}, Error: ${data.error}`)
      return null
    }
  } catch (error) {
    logTest('GET pricing config', false, `Network error: ${error.message}`)
    return null
  }
}

async function testUpdatePricingConfig() {
  logSection('Testing POST /api/globals/dtf-pricing')

  try {
    const { response, data } = await makeRequest('/api/globals/dtf-pricing', {
      method: 'POST',
      body: JSON.stringify(TEST_CONFIG),
    })

    if (response.ok) {
      logTest('POST pricing config', true, `Status: ${response.status}`)
      logTest('Configuration saved', data.success === true, `Success: ${data.success}`)
      return data.data
    } else {
      logTest('POST pricing config', false, `Status: ${response.status}, Error: ${data.error}`)
      if (data.details && data.details.errors) {
        console.log('Validation errors:', data.details.errors)
      }
      return null
    }
  } catch (error) {
    logTest('POST pricing config', false, `Network error: ${error.message}`)
    return null
  }
}

async function testQuoteCalculation() {
  logSection('Testing POST /api/dtf-pricing/quote')

  const testQuotes = [
    {
      name: 'Small item quote',
      request: { width: 3, height: 3, quantity: 10 },
      expectedTier: 'Small',
    },
    {
      name: 'Medium item quote',
      request: { width: 7, height: 7, quantity: 5 },
      expectedTier: 'Medium',
    },
    {
      name: 'Large item quote',
      request: { width: 15, height: 15, quantity: 1 },
      expectedTier: 'Large',
    },
    {
      name: 'Rush order quote',
      request: { width: 5, height: 5, quantity: 2, isRushOrder: true },
      expectedTier: 'Medium',
    },
  ]

  for (const testQuote of testQuotes) {
    try {
      const { response, data } = await makeRequest('/api/dtf-pricing/quote', {
        method: 'POST',
        body: JSON.stringify(testQuote.request),
      })

      if (response.ok && data.success) {
        logTest(testQuote.name, true, `Total: ${data.data.currency} ${data.data.total}`)

        if (data.data.breakdown && data.data.breakdown.appliedTier === testQuote.expectedTier) {
          logTest(
            `${testQuote.name} - Correct tier`,
            true,
            `Applied: ${data.data.breakdown.appliedTier}`,
          )
        } else {
          logTest(
            `${testQuote.name} - Correct tier`,
            false,
            `Expected: ${testQuote.expectedTier}, Got: ${data.data.breakdown?.appliedTier}`,
          )
        }
      } else {
        logTest(testQuote.name, false, `Status: ${response.status}, Error: ${data.error}`)
      }
    } catch (error) {
      logTest(testQuote.name, false, `Network error: ${error.message}`)
    }
  }
}

async function testValidation() {
  logSection('Testing Input Validation')

  const invalidConfigs = [
    {
      name: 'Invalid pricing model',
      config: { ...TEST_CONFIG, pricingModel: 'invalid_model' },
    },
    {
      name: 'Negative setup fee',
      config: { ...TEST_CONFIG, basePricing: { ...TEST_CONFIG.basePricing, setupFee: -100 } },
    },
    {
      name: 'Invalid measurement unit',
      config: { ...TEST_CONFIG, measurementUnit: 'invalid_unit' },
    },
    {
      name: 'Empty tier name',
      config: {
        ...TEST_CONFIG,
        sizeTiers: [
          {
            ...TEST_CONFIG.sizeTiers[0],
            tierName: '',
          },
        ],
      },
    },
  ]

  for (const invalidConfig of invalidConfigs) {
    try {
      const { response, data } = await makeRequest('/api/globals/dtf-pricing', {
        method: 'POST',
        body: JSON.stringify(invalidConfig.config),
      })

      if (response.status === 400 && !data.success) {
        logTest(invalidConfig.name, true, 'Correctly rejected')
      } else {
        logTest(
          invalidConfig.name,
          false,
          `Should have been rejected but got status: ${response.status}`,
        )
      }
    } catch (error) {
      logTest(invalidConfig.name, false, `Network error: ${error.message}`)
    }
  }
}

async function testInvalidQuotes() {
  logSection('Testing Quote Validation')

  const invalidQuotes = [
    {
      name: 'Negative width',
      request: { width: -5, height: 5, quantity: 1 },
    },
    {
      name: 'Zero quantity',
      request: { width: 5, height: 5, quantity: 0 },
    },
    {
      name: 'Non-integer quantity',
      request: { width: 5, height: 5, quantity: 1.5 },
    },
    {
      name: 'Excessive dimensions',
      request: { width: 50000, height: 50000, quantity: 1 },
    },
  ]

  for (const invalidQuote of invalidQuotes) {
    try {
      const { response, data } = await makeRequest('/api/dtf-pricing/quote', {
        method: 'POST',
        body: JSON.stringify(invalidQuote.request),
      })

      if (response.status === 400 && !data.success) {
        logTest(invalidQuote.name, true, 'Correctly rejected')
      } else {
        logTest(
          invalidQuote.name,
          false,
          `Should have been rejected but got status: ${response.status}`,
        )
      }
    } catch (error) {
      logTest(invalidQuote.name, false, `Network error: ${error.message}`)
    }
  }
}

async function testRateLimit() {
  logSection('Testing Rate Limiting')

  console.log('Making rapid requests to test rate limiting...')

  let successCount = 0
  let rateLimitedCount = 0

  // Make 10 rapid requests
  const promises = Array.from({ length: 10 }, async (_, i) => {
    try {
      const { response } = await makeRequest('/api/globals/dtf-pricing')
      if (response.status === 429) {
        rateLimitedCount++
      } else if (response.ok) {
        successCount++
      }
    } catch (error) {
      // Network errors don't count
    }
  })

  await Promise.all(promises)

  logTest(
    'Rate limiting active',
    rateLimitedCount > 0 || successCount <= 10,
    `Success: ${successCount}, Rate limited: ${rateLimitedCount}`,
  )
}

async function testErrorHandling() {
  logSection('Testing Error Handling')

  // Test malformed JSON
  try {
    const response = await fetch(`${BASE_URL}/api/globals/dtf-pricing`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: 'invalid json',
    })

    const data = await response.json()
    logTest(
      'Malformed JSON handling',
      response.status === 400 && !data.success,
      `Status: ${response.status}`,
    )
  } catch (error) {
    logTest('Malformed JSON handling', false, `Error: ${error.message}`)
  }

  // Test oversized payload
  try {
    const largePayload = { data: 'x'.repeat(2 * 1024 * 1024) } // 2MB
    const response = await fetch(`${BASE_URL}/api/globals/dtf-pricing`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(largePayload),
    })

    const data = await response.json()
    logTest(
      'Large payload handling',
      response.status === 413 && !data.success,
      `Status: ${response.status}`,
    )
  } catch (error) {
    logTest('Large payload handling', false, `Error: ${error.message}`)
  }
}

// Main test runner
async function runTests() {
  console.log('🚀 DTF Pricing Production Test Suite')
  console.log('=====================================\n')

  console.log('Testing server availability...')
  try {
    const response = await fetch(`${BASE_URL}/admin`)
    if (!response.ok) {
      console.log('❌ Server not available. Please start the development server.')
      process.exit(1)
    }
    console.log('✅ Server is running\n')
  } catch (error) {
    console.log('❌ Cannot connect to server. Please start the development server.')
    process.exit(1)
  }

  // Run all tests
  await testGetPricingConfig()
  await testUpdatePricingConfig()
  await testQuoteCalculation()
  await testValidation()
  await testInvalidQuotes()
  await testRateLimit()
  await testErrorHandling()

  console.log('\n🎉 Test suite completed!')
  console.log('\nNote: Some tests may fail if authentication is required.')
  console.log('For full testing, ensure you are logged in as an admin user.')
}

// Run tests if this script is executed directly
import { fileURLToPath } from 'url'
import { dirname } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

if (import.meta.url === `file://${process.argv[1]}`) {
  runTests().catch(console.error)
}

export { runTests }

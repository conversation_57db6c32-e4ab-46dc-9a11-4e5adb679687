'use client'

import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { useAuth } from '@payloadcms/ui'
import { DTFPricingConfig, DTFSizeTier, DTFPricingValidation } from '@/types/dtf-pricing'

// Production-ready validation utilities
const validatePricingConfig = (config: DTFPricingConfig): DTFPricingValidation => {
  const errors: string[] = []
  const warnings: string[] = []

  // Check if pricing is active but no size tiers
  if (config.isActive && (!config.sizeTiers || config.sizeTiers.length === 0)) {
    errors.push('At least one size tier is required when pricing system is active')
  }

  // Check for active size tiers
  const activeTiers = config.sizeTiers?.filter((tier) => tier.isActive) || []
  if (config.isActive && activeTiers.length === 0) {
    errors.push('At least one active size tier is required')
  }

  // Check for overlapping size tiers
  for (let i = 0; i < activeTiers.length; i++) {
    for (let j = i + 1; j < activeTiers.length; j++) {
      const tier1 = activeTiers[i]
      const tier2 = activeTiers[j]

      if (!tier1 || !tier2 || !tier1.dimensions || !tier2.dimensions) {
        continue
      }

      const width1Min = tier1.dimensions.minWidth
      const width1Max = tier1.dimensions.maxWidth || Infinity
      const height1Min = tier1.dimensions.minHeight
      const height1Max = tier1.dimensions.maxHeight || Infinity

      const width2Min = tier2.dimensions.minWidth
      const width2Max = tier2.dimensions.maxWidth || Infinity
      const height2Min = tier2.dimensions.minHeight
      const height2Max = tier2.dimensions.maxHeight || Infinity

      const widthOverlap = width1Min < width2Max && width2Min < width1Max
      const heightOverlap = height1Min < height2Max && height2Min < height1Max

      if (widthOverlap && heightOverlap) {
        errors.push(
          `Size tiers "${tier1.tierName}" and "${tier2.tierName}" have overlapping dimensions`,
        )
      }
    }
  }

  // Check for reasonable pricing values
  config.sizeTiers?.forEach((tier, index) => {
    if (tier.pricing.basePrice > 100000) {
      warnings.push(`Size tier ${index + 1} has a very high base price`)
    }
    if (tier.pricing.pricePerUnit && tier.pricing.pricePerUnit > 1000) {
      warnings.push(`Size tier ${index + 1} has a very high price per unit`)
    }
  })

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  }
}

// Error boundary component
class DTFPricingErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error }
  }

  override componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('DTF Pricing Admin Error:', error, errorInfo)
    // In production, send to error reporting service
  }

  override render() {
    if (this.state.hasError) {
      return (
        <div className="error-boundary">
          <h2>Something went wrong</h2>
          <p>An error occurred while loading the DTF pricing management interface.</p>
          <details>
            <summary>Error details</summary>
            <pre>{this.state.error?.message}</pre>
          </details>
          <button onClick={() => this.setState({ hasError: false })}>Try again</button>
        </div>
      )
    }

    return this.props.children
  }
}

// Loading component
const LoadingSpinner: React.FC<{ message?: string }> = ({ message = 'Loading...' }) => (
  <div className="loading-container">
    <div className="spinner"></div>
    <p>{message}</p>
    <style jsx>{`
      .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 3rem;
        color: #6c757d;
      }
      .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #007bff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 1rem;
      }
      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
    `}</style>
  </div>
)

// Alert component
const Alert: React.FC<{
  type: 'success' | 'error' | 'warning' | 'info'
  message: string
  onClose?: () => void
}> = ({ type, message, onClose }) => (
  <div className={`alert alert-${type}`}>
    <span>{message}</span>
    {onClose && (
      <button className="alert-close" onClick={onClose}>
        ×
      </button>
    )}
    <style jsx>{`
      .alert {
        padding: 0.75rem 1rem;
        border-radius: 4px;
        margin-bottom: 1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .alert-success {
        background: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
      }
      .alert-error {
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
      }
      .alert-warning {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
      }
      .alert-info {
        background: #d1ecf1;
        border: 1px solid #bee5eb;
        color: #0c5460;
      }
      .alert-close {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        padding: 0;
        margin-left: 1rem;
      }
    `}</style>
  </div>
)

// Main component
const ProductionDTFPricingAdmin: React.FC = () => {
  const { user } = useAuth()
  const [pricingConfig, setPricingConfig] = useState<DTFPricingConfig | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [activeTab, setActiveTab] = useState<'overview' | 'size-tiers' | 'validation'>('overview')
  const [alerts, setAlerts] = useState<
    Array<{ id: string; type: 'success' | 'error' | 'warning' | 'info'; message: string }>
  >([])
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  // Validation state
  const validation = useMemo(() => {
    if (!pricingConfig) return { isValid: true, errors: [], warnings: [] }
    return validatePricingConfig(pricingConfig)
  }, [pricingConfig])

  // Add alert utility
  const addAlert = useCallback(
    (type: 'success' | 'error' | 'warning' | 'info', message: string) => {
      const id = Date.now().toString()
      setAlerts((prev) => [...prev, { id, type, message }])

      // Auto-remove success and info alerts after 5 seconds
      if (type === 'success' || type === 'info') {
        setTimeout(() => {
          setAlerts((prev) => prev.filter((alert) => alert.id !== id))
        }, 5000)
      }
    },
    [],
  )

  // Remove alert
  const removeAlert = useCallback((id: string) => {
    setAlerts((prev) => prev.filter((alert) => alert.id !== id))
  }, [])

  // Fetch pricing configuration
  const fetchPricingConfig = useCallback(async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/globals/dtf-pricing', {
        headers: {
          'Cache-Control': 'no-cache',
        },
      })

      if (response.ok) {
        const data = await response.json()
        setPricingConfig(data)
        addAlert('info', 'Pricing configuration loaded successfully')
      } else if (response.status === 404) {
        // Create default configuration
        const defaultConfig: Partial<DTFPricingConfig> = {
          isActive: false,
          pricingModel: 'per_square_inch',
          measurementUnit: 'inches',
          currency: 'INR',
          basePricing: {
            minimumOrderValue: 500,
            setupFee: 100,
            rushOrderMultiplier: 1.5,
          },
          sizeTiers: [],
          quantityDiscounts: [],
          materialOptions: [],
          additionalServices: [],
          paymentTerms: {
            defaultTerms: 'full_upfront',
            minimumAdvancePercentage: 50,
          },
        }
        setPricingConfig(defaultConfig as DTFPricingConfig)
        addAlert('warning', 'No existing configuration found. Using default settings.')
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (error) {
      console.error('Failed to fetch pricing config:', error)
      addAlert(
        'error',
        `Failed to load pricing configuration: ${error instanceof Error ? error.message : 'Unknown error'}`,
      )
    } finally {
      setLoading(false)
    }
  }, [addAlert])

  // Save pricing configuration
  const savePricingConfig = useCallback(async () => {
    if (!pricingConfig) return

    // Validate before saving
    const validationResult = validatePricingConfig(pricingConfig)
    if (!validationResult.isValid) {
      addAlert('error', `Cannot save: ${validationResult.errors.join(', ')}`)
      return
    }

    try {
      setSaving(true)
      const response = await fetch('/api/globals/dtf-pricing', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(pricingConfig),
      })

      if (response.ok) {
        const updatedData = await response.json()
        setPricingConfig(updatedData)
        setHasUnsavedChanges(false)
        addAlert('success', 'DTF pricing configuration saved successfully!')

        // Show warnings if any
        if (validationResult.warnings.length > 0) {
          validationResult.warnings.forEach((warning) => {
            addAlert('warning', warning)
          })
        }
      } else {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (error) {
      console.error('Failed to save pricing config:', error)
      addAlert(
        'error',
        `Failed to save configuration: ${error instanceof Error ? error.message : 'Unknown error'}`,
      )
    } finally {
      setSaving(false)
    }
  }, [pricingConfig, addAlert])

  // Load configuration on mount
  useEffect(() => {
    fetchPricingConfig()
  }, [fetchPricingConfig])

  // Track unsaved changes
  useEffect(() => {
    if (pricingConfig) {
      setHasUnsavedChanges(true)
    }
  }, [pricingConfig])

  // Warn about unsaved changes
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault()
        e.returnValue = ''
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => window.removeEventListener('beforeunload', handleBeforeUnload)
  }, [hasUnsavedChanges])

  // Check admin access after all hooks
  if (!user || user.role !== 'admin') {
    return (
      <div className="access-denied">
        <h2>Access Denied</h2>
        <p>Admin privileges required to manage DTF pricing.</p>
        <style jsx>{`
          .access-denied {
            padding: 2rem;
            text-align: center;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            border-radius: 8px;
            margin: 2rem;
          }
        `}</style>
      </div>
    )
  }

  if (loading) {
    return <LoadingSpinner message="Loading DTF pricing configuration..." />
  }

  if (!pricingConfig) {
    return (
      <div className="error-state">
        <h2>Configuration Error</h2>
        <p>Failed to load DTF pricing configuration.</p>
        <button onClick={fetchPricingConfig}>Retry</button>
      </div>
    )
  }

  return (
    <DTFPricingErrorBoundary>
      <div className="dtf-pricing-admin">
        {/* Alerts */}
        <div className="alerts-container">
          {alerts.map((alert) => (
            <Alert
              key={alert.id}
              type={alert.type}
              message={alert.message}
              onClose={() => removeAlert(alert.id)}
            />
          ))}
        </div>

        {/* Header */}
        <div className="header">
          <div className="header-content">
            <h1>DTF Printing Pricing Management</h1>
            <p>Configure pricing tiers, discounts, and options for DTF printing services</p>
            <div className="header-controls">
              <label className="status-toggle">
                <input
                  type="checkbox"
                  checked={pricingConfig.isActive}
                  onChange={(e) => {
                    setPricingConfig({ ...pricingConfig, isActive: e.target.checked })
                    if (e.target.checked && validation.errors.length > 0) {
                      addAlert(
                        'warning',
                        'Please fix validation errors before activating the pricing system',
                      )
                    }
                  }}
                />
                <span className={`toggle-label ${pricingConfig.isActive ? 'active' : 'inactive'}`}>
                  {pricingConfig.isActive ? 'Pricing System Active' : 'Pricing System Inactive'}
                </span>
              </label>
              {hasUnsavedChanges && <span className="unsaved-indicator">Unsaved changes</span>}
            </div>
          </div>
        </div>

        {/* Validation Summary */}
        {!validation.isValid && (
          <div className="validation-summary">
            <h3>⚠️ Validation Issues</h3>
            <ul>
              {validation.errors.map((error, index) => (
                <li key={index} className="error">
                  {error}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Navigation Tabs */}
        <div className="tabs">
          <button
            className={`tab ${activeTab === 'overview' ? 'active' : ''}`}
            onClick={() => setActiveTab('overview')}
          >
            Overview
          </button>
          <button
            className={`tab ${activeTab === 'size-tiers' ? 'active' : ''}`}
            onClick={() => setActiveTab('size-tiers')}
          >
            Size Tiers ({pricingConfig.sizeTiers?.length || 0})
          </button>
          <button
            className={`tab ${activeTab === 'validation' ? 'active' : ''} ${!validation.isValid ? 'has-errors' : ''}`}
            onClick={() => setActiveTab('validation')}
          >
            Validation {!validation.isValid && '⚠️'}
          </button>
        </div>

        {/* Tab Content */}
        <div className="tab-content">
          {activeTab === 'overview' && (
            <OverviewTab
              config={pricingConfig}
              onChange={setPricingConfig}
              _validation={validation}
            />
          )}
          {activeTab === 'size-tiers' && (
            <SizeTiersTab
              config={pricingConfig}
              onChange={setPricingConfig}
              _validation={validation}
            />
          )}
          {activeTab === 'validation' && <ValidationTab validation={validation} />}
        </div>

        {/* Actions */}
        <div className="actions">
          <button
            className="btn btn-secondary"
            onClick={() => {
              if (hasUnsavedChanges && confirm('Discard unsaved changes?')) {
                fetchPricingConfig()
              }
            }}
            disabled={saving || !hasUnsavedChanges}
          >
            Reset Changes
          </button>
          <button
            className="btn btn-primary"
            onClick={savePricingConfig}
            disabled={saving || !validation.isValid}
          >
            {saving ? 'Saving...' : 'Save Configuration'}
          </button>
        </div>

        <style jsx>{`
          .dtf-pricing-admin {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
          }
          .alerts-container {
            margin-bottom: 1rem;
          }
          .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          }
          .header h1 {
            margin: 0 0 0.5rem 0;
            font-size: 2rem;
            font-weight: 700;
          }
          .header p {
            margin: 0 0 1rem 0;
            opacity: 0.9;
          }
          .header-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
          }
          .status-toggle {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
          }
          .toggle-label.active {
            color: #4ade80;
            font-weight: 600;
          }
          .toggle-label.inactive {
            color: #f87171;
            font-weight: 600;
          }
          .unsaved-indicator {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
          }
          .validation-summary {
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
          }
          .validation-summary h3 {
            margin: 0 0 0.5rem 0;
            color: #dc2626;
          }
          .validation-summary ul {
            margin: 0;
            padding-left: 1.5rem;
          }
          .validation-summary .error {
            color: #dc2626;
            margin-bottom: 0.25rem;
          }
          .tabs {
            display: flex;
            border-bottom: 2px solid #e5e7eb;
            margin-bottom: 2rem;
          }
          .tab {
            padding: 1rem 1.5rem;
            background: none;
            border: none;
            border-bottom: 3px solid transparent;
            cursor: pointer;
            font-weight: 500;
            color: #6b7280;
            transition: all 0.2s;
            position: relative;
          }
          .tab:hover {
            color: #374151;
            background: #f9fafb;
          }
          .tab.active {
            color: #3b82f6;
            border-bottom-color: #3b82f6;
            background: #eff6ff;
          }
          .tab.has-errors {
            color: #dc2626;
          }
          .tab-content {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          }
          .actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            padding-top: 2rem;
            border-top: 1px solid #e5e7eb;
          }
          .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.2s;
            font-size: 0.875rem;
          }
          .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
          .btn-primary {
            background: #3b82f6;
            color: white;
          }
          .btn-primary:hover:not(:disabled) {
            background: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
          }
          .btn-secondary {
            background: #6b7280;
            color: white;
          }
          .btn-secondary:hover:not(:disabled) {
            background: #4b5563;
          }
          .error-state {
            text-align: center;
            padding: 3rem;
            color: #dc2626;
          }
        `}</style>
      </div>
    </DTFPricingErrorBoundary>
  )
}

// Overview Tab Component
const OverviewTab: React.FC<{
  config: DTFPricingConfig
  onChange: (config: DTFPricingConfig) => void
  _validation: DTFPricingValidation
}> = ({ config, onChange, _validation }) => (
  <div className="overview-tab">
    <div className="section">
      <h3>Basic Configuration</h3>
      <div className="form-grid">
        <div className="form-group">
          <label>Pricing Model</label>
          <select
            value={config.pricingModel}
            onChange={(e) =>
              onChange({
                ...config,
                pricingModel: e.target.value as DTFPricingConfig['pricingModel'],
              })
            }
          >
            <option value="per_square_inch">Per Square Inch</option>
            <option value="per_linear_inch">Per Linear Inch</option>
            <option value="flat_rate_by_size">Flat Rate by Size</option>
            <option value="tiered_pricing">Tiered Pricing</option>
          </select>
        </div>
        <div className="form-group">
          <label>Measurement Unit</label>
          <select
            value={config.measurementUnit}
            onChange={(e) =>
              onChange({
                ...config,
                measurementUnit: e.target.value as DTFPricingConfig['measurementUnit'],
              })
            }
          >
            <option value="inches">Inches</option>
            <option value="centimeters">Centimeters</option>
            <option value="millimeters">Millimeters</option>
          </select>
        </div>
        <div className="form-group">
          <label>Currency</label>
          <select
            value={config.currency}
            onChange={(e) =>
              onChange({ ...config, currency: e.target.value as DTFPricingConfig['currency'] })
            }
          >
            <option value="INR">INR (₹)</option>
            <option value="USD">USD ($)</option>
            <option value="EUR">EUR (€)</option>
          </select>
        </div>
      </div>
    </div>

    <div className="section">
      <h3>Base Pricing</h3>
      <div className="form-grid">
        <div className="form-group">
          <label>Minimum Order Value</label>
          <input
            type="number"
            min="0"
            step="1"
            value={config.basePricing.minimumOrderValue || 0}
            onChange={(e) =>
              onChange({
                ...config,
                basePricing: {
                  ...config.basePricing,
                  minimumOrderValue: parseFloat(e.target.value) || 0,
                },
              })
            }
          />
        </div>
        <div className="form-group">
          <label>Setup Fee</label>
          <input
            type="number"
            min="0"
            step="1"
            value={config.basePricing.setupFee || 0}
            onChange={(e) =>
              onChange({
                ...config,
                basePricing: {
                  ...config.basePricing,
                  setupFee: parseFloat(e.target.value) || 0,
                },
              })
            }
          />
        </div>
        <div className="form-group">
          <label>Rush Order Multiplier</label>
          <input
            type="number"
            min="1"
            max="10"
            step="0.1"
            value={config.basePricing.rushOrderMultiplier || 1.5}
            onChange={(e) =>
              onChange({
                ...config,
                basePricing: {
                  ...config.basePricing,
                  rushOrderMultiplier: parseFloat(e.target.value) || 1.5,
                },
              })
            }
          />
        </div>
      </div>
    </div>

    <div className="section">
      <h3>Payment Terms</h3>
      <div className="form-grid">
        <div className="form-group">
          <label>Default Payment Terms</label>
          <select
            value={config.paymentTerms.defaultTerms}
            onChange={(e) =>
              onChange({
                ...config,
                paymentTerms: {
                  ...config.paymentTerms,
                  defaultTerms: e.target.value as DTFPricingConfig['paymentTerms']['defaultTerms'],
                },
              })
            }
          >
            <option value="full_upfront">100% Upfront</option>
            <option value="split_payment">50% Advance, 50% on Delivery</option>
            <option value="custom_split">Custom Split</option>
          </select>
        </div>
        <div className="form-group">
          <label>Minimum Advance Percentage</label>
          <input
            type="number"
            min="0"
            max="100"
            value={config.paymentTerms.minimumAdvancePercentage || 50}
            onChange={(e) =>
              onChange({
                ...config,
                paymentTerms: {
                  ...config.paymentTerms,
                  minimumAdvancePercentage: parseInt(e.target.value) || 50,
                },
              })
            }
          />
        </div>
      </div>
    </div>

    <style jsx>{`
      .section {
        margin-bottom: 2rem;
      }
      .section h3 {
        margin-bottom: 1rem;
        color: #374151;
        font-weight: 600;
      }
      .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
      }
      .form-group {
        display: flex;
        flex-direction: column;
      }
      .form-group label {
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: #374151;
      }
      .form-group input,
      .form-group select {
        padding: 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        font-size: 0.875rem;
        transition: border-color 0.2s;
      }
      .form-group input:focus,
      .form-group select:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }
    `}</style>
  </div>
)

// Size Tiers Tab Component
const SizeTiersTab: React.FC<{
  config: DTFPricingConfig
  onChange: (config: DTFPricingConfig) => void
  _validation: DTFPricingValidation
}> = ({ config, onChange, _validation }) => {
  const addSizeTier = () => {
    const newTier: DTFSizeTier = {
      tierName: `Tier ${(config.sizeTiers?.length || 0) + 1}`,
      dimensions: {
        minWidth: 0.1,
        minHeight: 0.1,
      },
      pricing: {
        basePrice: 10,
      },
      isActive: true,
    }

    onChange({
      ...config,
      sizeTiers: [...(config.sizeTiers || []), newTier],
    })
  }

  const updateSizeTier = (index: number, updatedTier: DTFSizeTier) => {
    const updatedTiers = [...(config.sizeTiers || [])]
    updatedTiers[index] = updatedTier
    onChange({
      ...config,
      sizeTiers: updatedTiers,
    })
  }

  const removeSizeTier = (index: number) => {
    const updatedTiers = (config.sizeTiers || []).filter((_, i) => i !== index)
    onChange({
      ...config,
      sizeTiers: updatedTiers,
    })
  }

  return (
    <div className="size-tiers-tab">
      <div className="section-header">
        <h3>Size-Based Pricing Tiers</h3>
        <button className="btn btn-primary" onClick={addSizeTier}>
          Add Size Tier
        </button>
      </div>

      {!config.sizeTiers || config.sizeTiers.length === 0 ? (
        <div className="empty-state">
          <h4>No Size Tiers Configured</h4>
          <p>Add size tiers to define pricing based on dimensions</p>
          <button className="btn btn-primary" onClick={addSizeTier}>
            Add Your First Size Tier
          </button>
        </div>
      ) : (
        <div className="tiers-list">
          {config.sizeTiers.map((tier, index) => (
            <div key={index} className="tier-card">
              <div className="tier-header">
                <h4>Size Tier {index + 1}</h4>
                <div className="tier-actions">
                  <label className="toggle">
                    <input
                      type="checkbox"
                      checked={tier.isActive}
                      onChange={(e) =>
                        updateSizeTier(index, { ...tier, isActive: e.target.checked })
                      }
                    />
                    Active
                  </label>
                  <button className="btn btn-danger btn-sm" onClick={() => removeSizeTier(index)}>
                    Remove
                  </button>
                </div>
              </div>

              <div className="tier-content">
                <div className="form-group">
                  <label>Tier Name</label>
                  <input
                    type="text"
                    value={tier.tierName}
                    placeholder="e.g., Small, Medium, Large"
                    onChange={(e) => updateSizeTier(index, { ...tier, tierName: e.target.value })}
                  />
                </div>

                <div className="dimensions-grid">
                  <div className="form-group">
                    <label>Min Width ({config.measurementUnit})</label>
                    <input
                      type="number"
                      min="0.1"
                      step="0.1"
                      value={tier.dimensions.minWidth}
                      onChange={(e) =>
                        updateSizeTier(index, {
                          ...tier,
                          dimensions: {
                            ...tier.dimensions,
                            minWidth: parseFloat(e.target.value) || 0.1,
                          },
                        })
                      }
                    />
                  </div>
                  <div className="form-group">
                    <label>Max Width ({config.measurementUnit}) - Optional</label>
                    <input
                      type="number"
                      min="0.1"
                      step="0.1"
                      value={tier.dimensions.maxWidth || ''}
                      placeholder="Unlimited"
                      onChange={(e) =>
                        updateSizeTier(index, {
                          ...tier,
                          dimensions: {
                            ...tier.dimensions,
                            maxWidth: e.target.value ? parseFloat(e.target.value) : undefined,
                          },
                        })
                      }
                    />
                  </div>
                  <div className="form-group">
                    <label>Min Height ({config.measurementUnit})</label>
                    <input
                      type="number"
                      min="0.1"
                      step="0.1"
                      value={tier.dimensions.minHeight}
                      onChange={(e) =>
                        updateSizeTier(index, {
                          ...tier,
                          dimensions: {
                            ...tier.dimensions,
                            minHeight: parseFloat(e.target.value) || 0.1,
                          },
                        })
                      }
                    />
                  </div>
                  <div className="form-group">
                    <label>Max Height ({config.measurementUnit}) - Optional</label>
                    <input
                      type="number"
                      min="0.1"
                      step="0.1"
                      value={tier.dimensions.maxHeight || ''}
                      placeholder="Unlimited"
                      onChange={(e) =>
                        updateSizeTier(index, {
                          ...tier,
                          dimensions: {
                            ...tier.dimensions,
                            maxHeight: e.target.value ? parseFloat(e.target.value) : undefined,
                          },
                        })
                      }
                    />
                  </div>
                </div>

                <div className="pricing-grid">
                  <div className="form-group">
                    <label>Base Price ({config.currency})</label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={tier.pricing.basePrice}
                      onChange={(e) =>
                        updateSizeTier(index, {
                          ...tier,
                          pricing: {
                            ...tier.pricing,
                            basePrice: parseFloat(e.target.value) || 0,
                          },
                        })
                      }
                    />
                  </div>
                  <div className="form-group">
                    <label>Price Per Unit ({config.currency}) - Optional</label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={tier.pricing.pricePerUnit || ''}
                      placeholder="Based on pricing model"
                      onChange={(e) =>
                        updateSizeTier(index, {
                          ...tier,
                          pricing: {
                            ...tier.pricing,
                            pricePerUnit: e.target.value ? parseFloat(e.target.value) : undefined,
                          },
                        })
                      }
                    />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      <style jsx>{`
        .section-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 2rem;
        }
        .section-header h3 {
          margin: 0;
          color: #374151;
          font-weight: 600;
        }
        .empty-state {
          text-align: center;
          padding: 3rem;
          background: #f9fafb;
          border-radius: 8px;
          color: #6b7280;
        }
        .empty-state h4 {
          margin-bottom: 0.5rem;
          color: #374151;
        }
        .empty-state p {
          margin-bottom: 1.5rem;
        }
        .tiers-list {
          display: flex;
          flex-direction: column;
          gap: 1.5rem;
        }
        .tier-card {
          background: #f9fafb;
          border: 1px solid #e5e7eb;
          border-radius: 12px;
          padding: 1.5rem;
          transition: all 0.2s;
        }
        .tier-card:hover {
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        .tier-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1.5rem;
          padding-bottom: 1rem;
          border-bottom: 1px solid #e5e7eb;
        }
        .tier-header h4 {
          margin: 0;
          color: #374151;
          font-weight: 600;
        }
        .tier-actions {
          display: flex;
          align-items: center;
          gap: 1rem;
        }
        .toggle {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          font-size: 0.875rem;
          color: #6b7280;
        }
        .dimensions-grid,
        .pricing-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 1rem;
          margin-bottom: 1rem;
        }
        .form-group {
          display: flex;
          flex-direction: column;
        }
        .form-group label {
          margin-bottom: 0.5rem;
          font-weight: 500;
          color: #374151;
          font-size: 0.875rem;
        }
        .form-group input {
          padding: 0.75rem;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          font-size: 0.875rem;
          transition: border-color 0.2s;
        }
        .form-group input:focus {
          outline: none;
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        .btn {
          padding: 0.5rem 1rem;
          border: none;
          border-radius: 6px;
          cursor: pointer;
          font-weight: 500;
          transition: all 0.2s;
          font-size: 0.875rem;
        }
        .btn-sm {
          padding: 0.375rem 0.75rem;
          font-size: 0.75rem;
        }
        .btn-primary {
          background: #3b82f6;
          color: white;
        }
        .btn-primary:hover {
          background: #2563eb;
        }
        .btn-danger {
          background: #ef4444;
          color: white;
        }
        .btn-danger:hover {
          background: #dc2626;
        }
      `}</style>
    </div>
  )
}

// Validation Tab Component
const ValidationTab: React.FC<{
  validation: DTFPricingValidation
}> = ({ validation }) => (
  <div className="validation-tab">
    <div className="validation-status">
      <h3>{validation.isValid ? '✅ Configuration Valid' : '❌ Configuration Invalid'}</h3>
      <p>
        {validation.isValid
          ? 'Your DTF pricing configuration is valid and ready to use.'
          : 'Please fix the following issues before activating the pricing system.'}
      </p>
    </div>

    {validation.errors.length > 0 && (
      <div className="validation-section errors">
        <h4>❌ Errors ({validation.errors.length})</h4>
        <ul>
          {validation.errors.map((error, index) => (
            <li key={index}>{error}</li>
          ))}
        </ul>
      </div>
    )}

    {validation.warnings.length > 0 && (
      <div className="validation-section warnings">
        <h4>⚠️ Warnings ({validation.warnings.length})</h4>
        <ul>
          {validation.warnings.map((warning, index) => (
            <li key={index}>{warning}</li>
          ))}
        </ul>
      </div>
    )}

    {validation.isValid && validation.warnings.length === 0 && (
      <div className="validation-section success">
        <h4>🎉 Perfect Configuration!</h4>
        <p>
          No errors or warnings found. Your pricing configuration is optimized and ready for
          production use.
        </p>
      </div>
    )}

    <style jsx>{`
      .validation-status {
        background: ${validation.isValid ? '#f0fdf4' : '#fef2f2'};
        border: 1px solid ${validation.isValid ? '#bbf7d0' : '#fecaca'};
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 2rem;
      }
      .validation-status h3 {
        margin: 0 0 0.5rem 0;
        color: ${validation.isValid ? '#166534' : '#dc2626'};
      }
      .validation-status p {
        margin: 0;
        color: ${validation.isValid ? '#166534' : '#dc2626'};
      }
      .validation-section {
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 1rem;
      }
      .validation-section.errors {
        background: #fef2f2;
        border: 1px solid #fecaca;
      }
      .validation-section.warnings {
        background: #fffbeb;
        border: 1px solid #fed7aa;
      }
      .validation-section.success {
        background: #f0fdf4;
        border: 1px solid #bbf7d0;
      }
      .validation-section h4 {
        margin: 0 0 1rem 0;
      }
      .validation-section.errors h4 {
        color: #dc2626;
      }
      .validation-section.warnings h4 {
        color: #d97706;
      }
      .validation-section.success h4 {
        color: #166534;
      }
      .validation-section ul {
        margin: 0;
        padding-left: 1.5rem;
      }
      .validation-section li {
        margin-bottom: 0.5rem;
      }
      .validation-section.errors li {
        color: #dc2626;
      }
      .validation-section.warnings li {
        color: #d97706;
      }
      .validation-section.success p {
        color: #166534;
        margin: 0;
      }
    `}</style>
  </div>
)

export default ProductionDTFPricingAdmin

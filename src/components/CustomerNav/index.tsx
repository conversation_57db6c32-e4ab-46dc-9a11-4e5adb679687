'use client'

import React from 'react'
import { useAuth } from '@payloadcms/ui'

const CustomerNav: React.FC = () => {
  const { user } = useAuth()

  // Only show navigation for customer users
  if (!user || user.role !== 'customer') {
    return null
  }

  return (
    <div className="customer-nav">
      <style jsx>{`
        .customer-nav {
          background: #f8f9fa;
          border-bottom: 1px solid #e9ecef;
          padding: 1rem;
          margin-bottom: 1rem;
        }
        .customer-welcome {
          font-size: 1.1rem;
          font-weight: 600;
          color: #495057;
          margin-bottom: 0.5rem;
        }
        .customer-info {
          font-size: 0.9rem;
          color: #6c757d;
        }
      `}</style>
      <div className="customer-welcome">
        Welcome to your Customer Portal, {user.name || user.email}
      </div>
      <div className="customer-info">
        You can view your order history and track your orders below.
      </div>
    </div>
  )
}

export default CustomerNav

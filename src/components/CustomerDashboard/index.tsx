'use client'

import React from 'react'
import { useAuth } from '@payloadcms/ui'

const CustomerDashboard: React.FC = () => {
  const { user } = useAuth()

  // Only show for customer users
  if (!user || user.role !== 'customer') {
    return null
  }

  return (
    <div className="customer-dashboard">
      <style jsx>{`
        .customer-dashboard {
          max-width: 800px;
          margin: 0 auto;
          padding: 2rem;
        }
        .dashboard-header {
          text-align: center;
          margin-bottom: 2rem;
        }
        .dashboard-title {
          font-size: 2rem;
          font-weight: 700;
          color: #212529;
          margin-bottom: 0.5rem;
        }
        .dashboard-subtitle {
          font-size: 1.1rem;
          color: #6c757d;
        }
        .dashboard-content {
          background: #fff;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 2rem;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .info-section {
          margin-bottom: 1.5rem;
        }
        .info-title {
          font-size: 1.2rem;
          font-weight: 600;
          color: #495057;
          margin-bottom: 0.5rem;
        }
        .info-text {
          color: #6c757d;
          line-height: 1.6;
        }
        .orders-link {
          display: inline-block;
          background: #007bff;
          color: white;
          padding: 0.75rem 1.5rem;
          border-radius: 4px;
          text-decoration: none;
          font-weight: 500;
          transition: background-color 0.2s;
        }
        .orders-link:hover {
          background: #0056b3;
          color: white;
        }
      `}</style>

      <div className="dashboard-header">
        <h1 className="dashboard-title">Customer Portal</h1>
        <p className="dashboard-subtitle">Welcome, {user.name || user.email}</p>
      </div>

      <div className="dashboard-content">
        <div className="info-section">
          <h2 className="info-title">Your Order History</h2>
          <p className="info-text">
            View and track all your orders with Maddox Tees. You can see order status, tracking
            information, and download invoices for completed orders.
          </p>
          <a href="/admin/collections/orders" className="orders-link">
            View My Orders
          </a>
        </div>

        <div className="info-section">
          <h2 className="info-title">Need Help?</h2>
          <p className="info-text">
            If you have any questions about your orders or need assistance, please contact our
            customer support team.
          </p>
        </div>
      </div>
    </div>
  )
}

export default CustomerDashboard

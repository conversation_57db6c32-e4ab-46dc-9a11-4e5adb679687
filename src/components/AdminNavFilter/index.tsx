'use client'

import React from 'react'
import { useAuth } from '@payloadcms/ui'

interface AdminNavFilterProps {
  children: React.ReactNode
}

const AdminNavFilter: React.FC<AdminNavFilterProps> = ({ children }) => {
  const { user } = useAuth()

  // If user is a customer, hide admin navigation
  if (user && user.role === 'customer') {
    return (
      <style jsx global>{`
        /* Hide all navigation items except Orders for customer users */
        .nav__list .nav__item:not([data-collection='orders']) {
          display: none !important;
        }

        /* Hide global navigation items */
        .nav__list .nav__item[data-global] {
          display: none !important;
        }

        /* Hide admin-specific menu items */
        .nav__list .nav__item:has([href*='/admin/collections/users']),
        .nav__list .nav__item:has([href*='/admin/collections/customers']),
        .nav__list .nav__item:has([href*='/admin/collections/products']),
        .nav__list .nav__item:has([href*='/admin/collections/media']),
        .nav__list .nav__item:has([href*='/admin/collections/pages']),
        .nav__list .nav__item:has([href*='/admin/collections/posts']),
        .nav__list .nav__item:has([href*='/admin/collections/categories']),
        .nav__list .nav__item:has([href*='/admin/collections/testimonials']),
        .nav__list .nav__item:has([href*='/admin/collections/partners']),
        .nav__list .nav__item:has([href*='/admin/collections/recent-work']),
        .nav__list .nav__item:has([href*='/admin/collections/marketing-expenses']) {
          display: none !important;
        }

        /* Hide settings and other admin sections */
        .nav__list .nav__item:has([href*='/admin/account']),
        .nav__list .nav__item:has([href*='/admin/logout']) {
          /* Keep account and logout visible */
        }

        /* Style the remaining navigation for customer users */
        .nav__list {
          border-top: 3px solid #007bff;
          padding-top: 1rem;
        }

        .nav__list::before {
          content: 'Customer Portal';
          display: block;
          font-weight: 600;
          color: #007bff;
          margin-bottom: 0.5rem;
          padding: 0 1rem;
          font-size: 0.9rem;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
      `}</style>
    )
  }

  return <>{children}</>
}

export default AdminNavFilter

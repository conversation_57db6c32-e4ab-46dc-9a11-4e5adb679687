'use client'

import React, { useEffect } from 'react'
import { useAuth } from '@payloadcms/ui'

const DTFPricingNav: React.FC = () => {
  const { user } = useAuth()

  useEffect(() => {
    // Only show for admin users
    if (!user || user.role !== 'admin') return

    const addDTFPricingNavigation = () => {
      // Find the Products navigation group
      const navGroups = document.querySelectorAll('[data-test-id="nav-group"], .nav-group, .nav__group')
      
      navGroups.forEach((group) => {
        const groupLabel = group.querySelector('h3, .nav-group-label, .nav__group-label')
        
        if (groupLabel && groupLabel.textContent?.includes('Products')) {
          // Check if DTF Pricing link already exists
          const existingDTFLink = group.querySelector('a[href*="/admin/dtf-pricing"]')
          if (existingDTFLink) return

          // Find the nav list within this group
          const navList = group.querySelector('ul, .nav-list, .nav__list')
          
          if (navList) {
            // Create DTF Pricing navigation item
            const dtfPricingItem = document.createElement('li')
            dtfPricingItem.className = 'nav-item nav__item'
            
            const dtfPricingLink = document.createElement('a')
            dtfPricingLink.href = '/admin/dtf-pricing'
            dtfPricingLink.className = 'nav-link nav__link'
            dtfPricingLink.textContent = 'DTF Printing'
            dtfPricingLink.style.cssText = `
              display: flex;
              align-items: center;
              padding: 0.5rem 1rem;
              color: #6c757d;
              text-decoration: none;
              border-radius: 4px;
              transition: all 0.2s;
            `
            
            // Add hover effects
            dtfPricingLink.addEventListener('mouseenter', () => {
              dtfPricingLink.style.backgroundColor = '#f8f9fa'
              dtfPricingLink.style.color = '#495057'
            })
            
            dtfPricingLink.addEventListener('mouseleave', () => {
              dtfPricingLink.style.backgroundColor = 'transparent'
              dtfPricingLink.style.color = '#6c757d'
            })
            
            // Highlight if current page
            if (window.location.pathname.includes('/admin/dtf-pricing')) {
              dtfPricingLink.style.backgroundColor = '#e7f3ff'
              dtfPricingLink.style.color = '#0056b3'
              dtfPricingLink.style.fontWeight = '500'
            }
            
            dtfPricingItem.appendChild(dtfPricingLink)
            
            // Insert after Custom T-shirts if it exists, otherwise at the end
            const customTshirtsItem = navList.querySelector('a[href*="/admin/collections/custom-tshirts"]')?.closest('li')
            const solidTshirtsItem = navList.querySelector('a[href*="/admin/collections/solid-tshirts"]')?.closest('li')
            
            if (customTshirtsItem) {
              customTshirtsItem.insertAdjacentElement('afterend', dtfPricingItem)
            } else if (solidTshirtsItem) {
              solidTshirtsItem.insertAdjacentElement('afterend', dtfPricingItem)
            } else {
              navList.appendChild(dtfPricingItem)
            }
          }
        }
      })
    }

    // Add navigation immediately
    addDTFPricingNavigation()

    // Also add after a short delay to handle dynamic loading
    const timer = setTimeout(addDTFPricingNavigation, 1000)

    // Set up observer for dynamic content changes
    const observer = new MutationObserver(() => {
      addDTFPricingNavigation()
    })

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    })

    return () => {
      clearTimeout(timer)
      observer.disconnect()
    }
  }, [user])

  return null // This component doesn't render anything visible
}

export default DTFPricingNav

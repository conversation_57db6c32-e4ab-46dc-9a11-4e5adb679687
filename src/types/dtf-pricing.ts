export interface DTFPricingConfig {
  id: string
  isActive: boolean
  lastUpdated?: string
  updatedBy?: string
  pricingModel: 'per_square_inch' | 'per_linear_inch' | 'flat_rate_by_size' | 'tiered_pricing'
  measurementUnit: 'inches' | 'centimeters' | 'millimeters'
  currency: 'INR' | 'USD' | 'EUR'
  basePricing: {
    minimumOrderValue?: number
    setupFee?: number
    rushOrderMultiplier?: number
  }
  sizeTiers: DTFSizeTier[]
  quantityDiscounts: DTFQuantityDiscount[]
  materialOptions: DTFMaterialOption[]
  additionalServices: DTFAdditionalService[]
  paymentTerms: {
    defaultTerms: 'full_upfront' | 'split_payment' | 'custom_split'
    minimumAdvancePercentage?: number
  }
}

export interface DTFSizeTier {
  id?: string
  tierName: string
  dimensions: {
    minWidth: number
    maxWidth?: number
    minHeight: number
    maxHeight?: number
  }
  pricing: {
    basePrice: number
    pricePerUnit?: number
  }
  isActive: boolean
}

export interface DTFQuantityDiscount {
  id?: string
  minQuantity: number
  maxQuantity?: number
  discountType: 'percentage' | 'fixed_amount' | 'fixed_price'
  discountValue: number
  isActive: boolean
}

export interface DTFMaterialOption {
  id?: string
  materialName: string
  description?: string
  priceAdjustment: {
    adjustmentType: 'percentage_increase' | 'fixed_increase' | 'multiplier'
    adjustmentValue: number
  }
  isDefault?: boolean
  isActive: boolean
}

export interface DTFAdditionalService {
  id?: string
  serviceName: string
  description?: string
  pricingType: 'per_item' | 'per_order' | 'percentage'
  price: number
  isActive: boolean
}

// Quote calculation interfaces
export interface DTFQuoteRequest {
  width: number
  height: number
  quantity: number
  materialId?: string
  additionalServices?: string[]
  isRushOrder?: boolean
  measurementUnit?: 'inches' | 'centimeters' | 'millimeters'
}

export interface DTFQuoteResponse {
  subtotal: number
  discounts: {
    quantityDiscount?: number
    bulkDiscount?: number
  }
  additionalCosts: {
    setupFee?: number
    rushOrderFee?: number
    materialUpgrade?: number
    additionalServices?: number
  }
  total: number
  breakdown: {
    basePrice: number
    pricePerUnit?: number
    area?: number
    linearMeasurement?: number
    appliedTier?: string
    appliedDiscounts?: string[]
  }
  paymentTerms: {
    terms: string
    advanceAmount?: number
    balanceAmount?: number
  }
  currency: string
  validUntil?: string
}

// Admin interface types
export interface DTFPricingFormData {
  isActive: boolean
  pricingModel: string
  measurementUnit: string
  currency: string
  basePricing: {
    minimumOrderValue: number
    setupFee: number
    rushOrderMultiplier: number
  }
  sizeTiers: DTFSizeTier[]
  quantityDiscounts: DTFQuantityDiscount[]
  materialOptions: DTFMaterialOption[]
  additionalServices: DTFAdditionalService[]
  paymentTerms: {
    defaultTerms: string
    minimumAdvancePercentage: number
  }
}

export interface DTFPricingTableRow {
  id: string
  tierName: string
  dimensions: string
  basePrice: number
  pricePerUnit?: number
  isActive: boolean
}

export interface DTFPricingValidation {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

// API response types
export interface DTFPricingAPIResponse {
  success: boolean
  data?: DTFPricingConfig
  error?: string
  message?: string
}

export interface DTFQuoteAPIResponse {
  success: boolean
  data?: DTFQuoteResponse
  error?: string
  message?: string
}

// Utility types for calculations
export type MeasurementUnit = 'inches' | 'centimeters' | 'millimeters'
export type PricingModel = 'per_square_inch' | 'per_linear_inch' | 'flat_rate_by_size' | 'tiered_pricing'
export type DiscountType = 'percentage' | 'fixed_amount' | 'fixed_price'
export type AdjustmentType = 'percentage_increase' | 'fixed_increase' | 'multiplier'
export type PaymentTerms = 'full_upfront' | 'split_payment' | 'custom_split'

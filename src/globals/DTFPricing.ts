import type { GlobalConfig } from 'payload'
import { adminOnly } from '../access/adminOnly'

export const DTFPricing: GlobalConfig = {
  slug: 'dtf-pricing',
  access: {
    read: adminOnly,
    update: adminOnly,
  },
  admin: {
    group: 'Pricing Management',
    description: 'Configure DTF printing pricing tiers, discounts, and payment terms',
  },
  versions: {
    maxPerDoc: 50,
    drafts: false,
  },
  fields: [
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      required: true,
      admin: {
        description: 'Enable/disable DTF pricing system globally',
        position: 'sidebar',
      },
    },
    {
      name: 'lastUpdated',
      type: 'date',
      admin: {
        readOnly: true,
        description: 'Last time pricing was updated',
        position: 'sidebar',
      },
    },
    {
      name: 'updatedBy',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        readOnly: true,
        description: 'Admin who last updated pricing',
        position: 'sidebar',
      },
    },
    {
      name: 'version',
      type: 'text',
      defaultValue: '1.0.0',
      admin: {
        readOnly: true,
        description: 'Pricing configuration version',
        position: 'sidebar',
      },
    },
    {
      name: 'pricingModel',
      type: 'select',
      options: [
        { label: 'Per Square Inch', value: 'per_square_inch' },
        { label: 'Per Linear Inch', value: 'per_linear_inch' },
        { label: 'Flat Rate by Size Category', value: 'flat_rate_by_size' },
        { label: 'Tiered Pricing', value: 'tiered_pricing' },
      ],
      defaultValue: 'per_square_inch',
      required: true,
      admin: {
        description: 'Choose the pricing calculation method',
      },
    },
    {
      name: 'measurementUnit',
      type: 'select',
      options: [
        { label: 'Inches', value: 'inches' },
        { label: 'Centimeters', value: 'centimeters' },
        { label: 'Millimeters', value: 'millimeters' },
      ],
      defaultValue: 'inches',
      required: true,
      admin: {
        description: 'Default measurement unit for dimensions',
      },
    },
    {
      name: 'currency',
      type: 'select',
      options: [
        { label: 'INR (₹)', value: 'INR' },
        { label: 'USD ($)', value: 'USD' },
        { label: 'EUR (€)', value: 'EUR' },
      ],
      defaultValue: 'INR',
      required: true,
    },
    {
      name: 'basePricing',
      type: 'group',
      label: 'Base Pricing Configuration',
      fields: [
        {
          name: 'minimumOrderValue',
          type: 'number',
          min: 0,
          max: 1000000,
          admin: {
            description: 'Minimum order value required (in selected currency)',
            step: 1,
          },
          validate: (value) => {
            if (value !== undefined && value < 0) {
              return 'Minimum order value cannot be negative'
            }
            return true
          },
        },
        {
          name: 'setupFee',
          type: 'number',
          min: 0,
          max: 100000,
          admin: {
            description: 'One-time setup fee per order (in selected currency)',
            step: 1,
          },
          validate: (value) => {
            if (value !== undefined && value < 0) {
              return 'Setup fee cannot be negative'
            }
            return true
          },
        },
        {
          name: 'rushOrderMultiplier',
          type: 'number',
          min: 1,
          max: 10,
          defaultValue: 1.5,
          admin: {
            description: 'Multiplier for rush orders (e.g., 1.5 = 50% extra)',
            step: 0.1,
          },
          validate: (value) => {
            if (value !== undefined && value < 1) {
              return 'Rush order multiplier must be at least 1.0'
            }
            if (value !== undefined && value > 10) {
              return 'Rush order multiplier cannot exceed 10.0'
            }
            return true
          },
        },
      ],
    },
    {
      name: 'sizeTiers',
      type: 'array',
      label: 'Size-Based Pricing Tiers',
      admin: {
        description: 'Configure pricing based on size ranges',
      },
      fields: [
        {
          name: 'tierName',
          type: 'text',
          required: true,
          maxLength: 50,
          admin: {
            description: 'e.g., "Small", "Medium", "Large"',
          },
          validate: (value) => {
            if (!value || value.trim().length === 0) {
              return 'Tier name is required'
            }
            if (value.length > 50) {
              return 'Tier name cannot exceed 50 characters'
            }
            return true
          },
        },
        {
          name: 'dimensions',
          type: 'group',
          fields: [
            {
              name: 'minWidth',
              type: 'number',
              min: 0.1,
              max: 10000,
              required: true,
              admin: {
                step: 0.1,
                description: 'Minimum width in selected measurement unit',
              },
              validate: (value: any, { siblingData }: any) => {
                if (!value || value <= 0) {
                  return 'Minimum width must be greater than 0'
                }
                if (siblingData?.maxWidth && value >= siblingData.maxWidth) {
                  return 'Minimum width must be less than maximum width'
                }
                return true
              },
            },
            {
              name: 'maxWidth',
              type: 'number',
              min: 0.1,
              max: 10000,
              admin: {
                step: 0.1,
                description: 'Maximum width (leave empty for unlimited)',
              },
              validate: (value: any, { siblingData }: any) => {
                if (value !== undefined && value <= 0) {
                  return 'Maximum width must be greater than 0'
                }
                if (value && siblingData?.minWidth && value <= siblingData.minWidth) {
                  return 'Maximum width must be greater than minimum width'
                }
                return true
              },
            },
            {
              name: 'minHeight',
              type: 'number',
              min: 0.1,
              max: 10000,
              required: true,
              admin: {
                step: 0.1,
                description: 'Minimum height in selected measurement unit',
              },
              validate: (value: any, { siblingData }: any) => {
                if (!value || value <= 0) {
                  return 'Minimum height must be greater than 0'
                }
                if (siblingData?.maxHeight && value >= siblingData.maxHeight) {
                  return 'Minimum height must be less than maximum height'
                }
                return true
              },
            },
            {
              name: 'maxHeight',
              type: 'number',
              min: 0.1,
              max: 10000,
              admin: {
                step: 0.1,
                description: 'Maximum height (leave empty for unlimited)',
              },
              validate: (value: any, { siblingData }: any) => {
                if (value !== undefined && value <= 0) {
                  return 'Maximum height must be greater than 0'
                }
                if (value && siblingData?.minHeight && value <= siblingData.minHeight) {
                  return 'Maximum height must be greater than minimum height'
                }
                return true
              },
            },
          ],
        },
        {
          name: 'pricing',
          type: 'group',
          fields: [
            {
              name: 'basePrice',
              type: 'number',
              min: 0,
              max: 1000000,
              required: true,
              admin: {
                step: 0.01,
                description: 'Base price for this size tier (in selected currency)',
              },
              validate: (value: any) => {
                if (!value || value <= 0) {
                  return 'Base price must be greater than 0'
                }
                if (value > 1000000) {
                  return 'Base price cannot exceed 1,000,000'
                }
                return true
              },
            },
            {
              name: 'pricePerUnit',
              type: 'number',
              min: 0,
              max: 10000,
              admin: {
                step: 0.01,
                description: 'Price per square/linear inch (based on pricing model)',
              },
              validate: (value: any) => {
                if (value !== undefined && value < 0) {
                  return 'Price per unit cannot be negative'
                }
                if (value !== undefined && value > 10000) {
                  return 'Price per unit cannot exceed 10,000'
                }
                return true
              },
            },
          ],
        },
        {
          name: 'isActive',
          type: 'checkbox',
          defaultValue: true,
        },
      ],
    },
    {
      name: 'quantityDiscounts',
      type: 'array',
      label: 'Quantity-Based Discounts',
      admin: {
        description: 'Configure bulk order discounts',
      },
      fields: [
        {
          name: 'minQuantity',
          type: 'number',
          min: 1,
          required: true,
        },
        {
          name: 'maxQuantity',
          type: 'number',
          min: 1,
          admin: {
            description: 'Leave empty for unlimited',
          },
        },
        {
          name: 'discountType',
          type: 'select',
          options: [
            { label: 'Percentage Discount', value: 'percentage' },
            { label: 'Fixed Amount Off', value: 'fixed_amount' },
            { label: 'Fixed Price Per Unit', value: 'fixed_price' },
          ],
          defaultValue: 'percentage',
          required: true,
        },
        {
          name: 'discountValue',
          type: 'number',
          min: 0,
          required: true,
          admin: {
            description: 'Discount percentage (0-100) or fixed amount',
          },
        },
        {
          name: 'isActive',
          type: 'checkbox',
          defaultValue: true,
        },
      ],
    },
    {
      name: 'materialOptions',
      type: 'array',
      label: 'Material & Quality Options',
      admin: {
        description: 'Different material types with pricing adjustments',
      },
      fields: [
        {
          name: 'materialName',
          type: 'text',
          required: true,
          admin: {
            description: 'e.g., "Standard Vinyl", "Premium Vinyl", "Eco-Friendly"',
          },
        },
        {
          name: 'description',
          type: 'textarea',
          admin: {
            description: 'Description of material properties',
          },
        },
        {
          name: 'priceAdjustment',
          type: 'group',
          fields: [
            {
              name: 'adjustmentType',
              type: 'select',
              options: [
                { label: 'Percentage Increase', value: 'percentage_increase' },
                { label: 'Fixed Amount Increase', value: 'fixed_increase' },
                { label: 'Multiplier', value: 'multiplier' },
              ],
              defaultValue: 'percentage_increase',
              required: true,
            },
            {
              name: 'adjustmentValue',
              type: 'number',
              min: 0,
              required: true,
              admin: {
                description: 'Adjustment value (percentage, amount, or multiplier)',
              },
            },
          ],
        },
        {
          name: 'isDefault',
          type: 'checkbox',
          admin: {
            description: 'Mark as default material option',
          },
        },
        {
          name: 'isActive',
          type: 'checkbox',
          defaultValue: true,
        },
      ],
    },
    {
      name: 'additionalServices',
      type: 'array',
      label: 'Additional Services',
      admin: {
        description: 'Extra services like lamination, cutting, etc.',
      },
      fields: [
        {
          name: 'serviceName',
          type: 'text',
          required: true,
        },
        {
          name: 'description',
          type: 'textarea',
        },
        {
          name: 'pricingType',
          type: 'select',
          options: [
            { label: 'Per Item', value: 'per_item' },
            { label: 'Per Order', value: 'per_order' },
            { label: 'Percentage of Total', value: 'percentage' },
          ],
          defaultValue: 'per_item',
          required: true,
        },
        {
          name: 'price',
          type: 'number',
          min: 0,
          required: true,
        },
        {
          name: 'isActive',
          type: 'checkbox',
          defaultValue: true,
        },
      ],
    },
    {
      name: 'paymentTerms',
      type: 'group',
      label: 'Payment Terms Configuration',
      fields: [
        {
          name: 'defaultTerms',
          type: 'select',
          options: [
            { label: '100% Upfront', value: 'full_upfront' },
            { label: '50% Advance, 50% on Delivery', value: 'split_payment' },
            { label: '30% Advance, 70% on Delivery', value: 'custom_split' },
          ],
          defaultValue: 'full_upfront',
          required: true,
        },
        {
          name: 'minimumAdvancePercentage',
          type: 'number',
          min: 0,
          max: 100,
          defaultValue: 50,
          admin: {
            description: 'Minimum advance payment percentage required',
          },
        },
      ],
    },
  ],
  hooks: {
    beforeValidate: [
      ({ data }) => {
        // Validate that at least one size tier exists if pricing is active
        if (data.isActive && (!data.sizeTiers || data.sizeTiers.length === 0)) {
          throw new Error(
            'At least one active size tier is required when pricing system is enabled',
          )
        }

        // Validate that there are no overlapping size tiers
        if (data.sizeTiers && data.sizeTiers.length > 1) {
          const activeTiers = data.sizeTiers.filter((tier: any) => tier.isActive)
          for (let i = 0; i < activeTiers.length; i++) {
            for (let j = i + 1; j < activeTiers.length; j++) {
              const tier1 = activeTiers[i]
              const tier2 = activeTiers[j]

              // Check for overlapping dimensions
              const width1Min = tier1.dimensions?.minWidth || 0
              const width1Max = tier1.dimensions?.maxWidth || Infinity
              const height1Min = tier1.dimensions?.minHeight || 0
              const height1Max = tier1.dimensions?.maxHeight || Infinity

              const width2Min = tier2.dimensions?.minWidth || 0
              const width2Max = tier2.dimensions?.maxWidth || Infinity
              const height2Min = tier2.dimensions?.minHeight || 0
              const height2Max = tier2.dimensions?.maxHeight || Infinity

              // Check if ranges overlap
              const widthOverlap = width1Min < width2Max && width2Min < width1Max
              const heightOverlap = height1Min < height2Max && height2Min < height1Max

              if (widthOverlap && heightOverlap) {
                throw new Error(
                  `Size tiers "${tier1.tierName}" and "${tier2.tierName}" have overlapping dimensions`,
                )
              }
            }
          }
        }

        return data
      },
    ],
    beforeChange: [
      ({ data, req }) => {
        try {
          // Auto-update timestamp and user
          data.lastUpdated = new Date().toISOString()
          if (req.user) {
            data.updatedBy = req.user.id
          }

          // Increment version on significant changes
          if (data.sizeTiers || data.pricingModel || data.basePricing) {
            const currentVersion = data.version || '1.0.0'
            const versionParts = currentVersion.split('.').map(Number)
            versionParts[2] += 1 // Increment patch version
            data.version = versionParts.join('.')
          }

          return data
        } catch (error) {
          throw new Error(
            `Failed to update pricing configuration: ${error instanceof Error ? error.message : 'Unknown error'}`,
          )
        }
      },
    ],
    afterChange: [
      ({ doc, req }) => {
        // Log pricing changes for audit trail
        if (req.user) {
          console.log(`DTF Pricing updated by ${req.user.email} at ${new Date().toISOString()}`)
          console.log(`New version: ${doc.version}`)
        }
      },
    ],
  },
}

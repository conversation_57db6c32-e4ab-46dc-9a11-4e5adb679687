import type { GlobalConfig } from 'payload'
import { adminOnly } from '../access/adminOnly'

export const DTFPricing: GlobalConfig = {
  slug: 'dtf-pricing',
  access: {
    read: adminOnly,
    update: adminOnly,
  },
  admin: {
    group: 'Pricing Management',
    description: 'Configure DTF printing pricing tiers, discounts, and payment terms',
  },
  versions: {
    max: 50,
    drafts: false,
  },
  fields: [
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      required: true,
      admin: {
        description: 'Enable/disable DTF pricing system globally',
        position: 'sidebar',
      },
    },
    {
      name: 'lastUpdated',
      type: 'date',
      admin: {
        readOnly: true,
        description: 'Last time pricing was updated',
        position: 'sidebar',
      },
    },
    {
      name: 'updatedBy',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        readOnly: true,
        description: 'Admin who last updated pricing',
        position: 'sidebar',
      },
    },
    {
      name: 'version',
      type: 'text',
      defaultValue: '1.0.0',
      admin: {
        readOnly: true,
        description: 'Pricing configuration version',
        position: 'sidebar',
      },
    },
    {
      name: 'pricingModel',
      type: 'select',
      options: [
        { label: 'Per Square Inch', value: 'per_square_inch' },
        { label: 'Per Linear Inch', value: 'per_linear_inch' },
        { label: 'Flat Rate by Size Category', value: 'flat_rate_by_size' },
        { label: 'Tiered Pricing', value: 'tiered_pricing' },
      ],
      defaultValue: 'per_square_inch',
      required: true,
      admin: {
        description: 'Choose the pricing calculation method',
      },
    },
    {
      name: 'measurementUnit',
      type: 'select',
      options: [
        { label: 'Inches', value: 'inches' },
        { label: 'Centimeters', value: 'centimeters' },
        { label: 'Millimeters', value: 'millimeters' },
      ],
      defaultValue: 'inches',
      required: true,
      admin: {
        description: 'Default measurement unit for dimensions',
      },
    },
    {
      name: 'currency',
      type: 'select',
      options: [
        { label: 'INR (₹)', value: 'INR' },
        { label: 'USD ($)', value: 'USD' },
        { label: 'EUR (€)', value: 'EUR' },
      ],
      defaultValue: 'INR',
      required: true,
    },
    {
      name: 'basePricing',
      type: 'group',
      label: 'Base Pricing Configuration',
      fields: [
        {
          name: 'minimumOrderValue',
          type: 'number',
          min: 0,
          max: 1000000,
          admin: {
            description: 'Minimum order value required (in selected currency)',
            step: 1,
          },
        },
        {
          name: 'setupFee',
          type: 'number',
          min: 0,
          max: 100000,
          admin: {
            description: 'One-time setup fee per order (in selected currency)',
            step: 1,
          },
        },
        {
          name: 'rushOrderMultiplier',
          type: 'number',
          min: 1,
          max: 10,
          defaultValue: 1.5,
          admin: {
            description: 'Multiplier for rush orders (e.g., 1.5 = 50% extra)',
            step: 0.1,
          },
        },
      ],
    },
    {
      name: 'sizeTiers',
      type: 'array',
      label: 'Size-Based Pricing Tiers',
      admin: {
        description: 'Configure pricing based on size ranges',
      },
      fields: [
        {
          name: 'tierName',
          type: 'text',
          required: true,
          maxLength: 50,
          admin: {
            description: 'e.g., "Small", "Medium", "Large"',
          },
        },
        {
          name: 'dimensions',
          type: 'group',
          fields: [
            {
              name: 'minWidth',
              type: 'number',
              min: 0.1,
              max: 10000,
              required: true,
              admin: {
                step: 0.1,
                description: 'Minimum width in selected measurement unit',
              },
            },
            {
              name: 'maxWidth',
              type: 'number',
              min: 0.1,
              max: 10000,
              admin: {
                step: 0.1,
                description: 'Maximum width (leave empty for unlimited)',
              },
            },
            {
              name: 'minHeight',
              type: 'number',
              min: 0.1,
              max: 10000,
              required: true,
              admin: {
                step: 0.1,
                description: 'Minimum height in selected measurement unit',
              },
            },
            {
              name: 'maxHeight',
              type: 'number',
              min: 0.1,
              max: 10000,
              admin: {
                step: 0.1,
                description: 'Maximum height (leave empty for unlimited)',
              },
            },
          ],
        },
        {
          name: 'pricing',
          type: 'group',
          fields: [
            {
              name: 'basePrice',
              type: 'number',
              min: 0,
              max: 1000000,
              required: true,
              admin: {
                step: 0.01,
                description: 'Base price for this size tier (in selected currency)',
              },
            },
            {
              name: 'pricePerUnit',
              type: 'number',
              min: 0,
              max: 10000,
              admin: {
                step: 0.01,
                description: 'Price per square/linear inch (based on pricing model)',
              },
            },
          ],
        },
        {
          name: 'isActive',
          type: 'checkbox',
          defaultValue: true,
        },
      ],
    },
    {
      name: 'quantityDiscounts',
      type: 'array',
      label: 'Quantity-Based Discounts',
      admin: {
        description: 'Configure bulk order discounts',
      },
      fields: [
        {
          name: 'minQuantity',
          type: 'number',
          min: 1,
          required: true,
        },
        {
          name: 'maxQuantity',
          type: 'number',
          min: 1,
          admin: {
            description: 'Leave empty for unlimited',
          },
        },
        {
          name: 'discountType',
          type: 'select',
          options: [
            { label: 'Percentage Discount', value: 'percentage' },
            { label: 'Fixed Amount Off', value: 'fixed_amount' },
            { label: 'Fixed Price Per Unit', value: 'fixed_price' },
          ],
          defaultValue: 'percentage',
          required: true,
        },
        {
          name: 'discountValue',
          type: 'number',
          min: 0,
          required: true,
          admin: {
            description: 'Discount percentage (0-100) or fixed amount',
          },
        },
        {
          name: 'isActive',
          type: 'checkbox',
          defaultValue: true,
        },
      ],
    },
    {
      name: 'materialOptions',
      type: 'array',
      label: 'Material & Quality Options',
      admin: {
        description: 'Different material types with pricing adjustments',
      },
      fields: [
        {
          name: 'materialName',
          type: 'text',
          required: true,
          admin: {
            description: 'e.g., "Standard Vinyl", "Premium Vinyl", "Eco-Friendly"',
          },
        },
        {
          name: 'description',
          type: 'textarea',
          admin: {
            description: 'Description of material properties',
          },
        },
        {
          name: 'priceAdjustment',
          type: 'group',
          fields: [
            {
              name: 'adjustmentType',
              type: 'select',
              options: [
                { label: 'Percentage Increase', value: 'percentage_increase' },
                { label: 'Fixed Amount Increase', value: 'fixed_increase' },
                { label: 'Multiplier', value: 'multiplier' },
              ],
              defaultValue: 'percentage_increase',
              required: true,
            },
            {
              name: 'adjustmentValue',
              type: 'number',
              min: 0,
              required: true,
              admin: {
                description: 'Adjustment value (percentage, amount, or multiplier)',
              },
            },
          ],
        },
        {
          name: 'isDefault',
          type: 'checkbox',
          admin: {
            description: 'Mark as default material option',
          },
        },
        {
          name: 'isActive',
          type: 'checkbox',
          defaultValue: true,
        },
      ],
    },
    {
      name: 'additionalServices',
      type: 'array',
      label: 'Additional Services',
      admin: {
        description: 'Extra services like lamination, cutting, etc.',
      },
      fields: [
        {
          name: 'serviceName',
          type: 'text',
          required: true,
        },
        {
          name: 'description',
          type: 'textarea',
        },
        {
          name: 'pricingType',
          type: 'select',
          options: [
            { label: 'Per Item', value: 'per_item' },
            { label: 'Per Order', value: 'per_order' },
            { label: 'Percentage of Total', value: 'percentage' },
          ],
          defaultValue: 'per_item',
          required: true,
        },
        {
          name: 'price',
          type: 'number',
          min: 0,
          required: true,
        },
        {
          name: 'isActive',
          type: 'checkbox',
          defaultValue: true,
        },
      ],
    },
    {
      name: 'paymentTerms',
      type: 'group',
      label: 'Payment Terms Configuration',
      fields: [
        {
          name: 'defaultTerms',
          type: 'select',
          options: [
            { label: '100% Upfront', value: 'full_upfront' },
            { label: '50% Advance, 50% on Delivery', value: 'split_payment' },
            { label: '30% Advance, 70% on Delivery', value: 'custom_split' },
          ],
          defaultValue: 'full_upfront',
          required: true,
        },
        {
          name: 'minimumAdvancePercentage',
          type: 'number',
          min: 0,
          max: 100,
          defaultValue: 50,
          admin: {
            description: 'Minimum advance payment percentage required',
          },
        },
      ],
    },
  ],
  hooks: {
    beforeChange: [
      ({ data, req }) => {
        // Auto-update timestamp and user
        data.lastUpdated = new Date().toISOString()
        if (req.user) {
          data.updatedBy = req.user.id
        }

        // Increment version on significant changes
        if (data.sizeTiers || data.pricingModel || data.basePricing) {
          const currentVersion = data.version || '1.0.0'
          const versionParts = currentVersion.split('.').map(Number)
          versionParts[2] += 1 // Increment patch version
          data.version = versionParts.join('.')
        }

        return data
      },
    ],
    afterChange: [
      ({ doc, req }) => {
        // Log pricing changes for audit trail
        if (req.user) {
          console.log(`DTF Pricing updated by ${req.user.email} at ${new Date().toISOString()}`)
          console.log(`New version: ${doc.version}`)
        }
      },
    ],
  },
}

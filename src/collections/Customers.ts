import type { CollectionConfig } from 'payload'
import { adminOnly } from '../access/adminOnly'

export const Customers: CollectionConfig<'customers'> = {
  slug: 'customers',
  // Remove auth - this collection is now admin-only for customer data management
  access: {
    create: adminOnly, // Only admins can create customer records
    read: adminOnly, // Only admins can read customer records
    update: adminOnly, // Only admins can update customer records
    delete: adminOnly, // Only admins can delete customer records
  },
  admin: {
    useAsTitle: 'email',
    defaultColumns: ['email', 'companyName', 'createdAt'],
    group: 'Users',
  },
  fields: [
    {
      name: 'companyName',
      type: 'text',
      required: true,
    },
    {
      name: 'email',
      type: 'email',
      required: true,
      unique: true,
    },
    {
      name: 'firstName',
      type: 'text',
      required: true,
    },
    {
      name: 'lastName',
      type: 'text',
      required: true,
    },
    {
      name: 'phone',
      type: 'text',
      required: true,
    },
    {
      name: 'gstin',
      label: 'GSTIN',
      type: 'text',
      admin: {
        description: 'Goods and Services Tax Identification Number',
      },
    },
    {
      name: 'billingAddress',
      type: 'group',
      fields: [
        {
          name: 'line1',
          type: 'text',
          required: true,
        },
        {
          name: 'line2',
          type: 'text',
        },
        {
          name: 'city',
          type: 'text',
          required: true,
        },
        {
          name: 'state',
          type: 'text',
          required: true,
        },
        {
          name: 'postalCode',
          type: 'text',
          required: true,
        },
        {
          name: 'country',
          type: 'text',
          required: true,
          defaultValue: 'India',
        },
      ],
    },
    {
      name: 'shippingAddress',
      type: 'group',
      fields: [
        {
          name: 'sameAsBilling',
          type: 'checkbox',
          defaultValue: true,
        },
        {
          name: 'line1',
          type: 'text',
          admin: {
            condition: (data, siblingData) => !siblingData.sameAsBilling,
          },
        },
        {
          name: 'line2',
          type: 'text',
          admin: {
            condition: (data, siblingData) => !siblingData.sameAsBilling,
          },
        },
        {
          name: 'city',
          type: 'text',
          admin: {
            condition: (data, siblingData) => !siblingData.sameAsBilling,
          },
        },
        {
          name: 'state',
          type: 'text',
          admin: {
            condition: (data, siblingData) => !siblingData.sameAsBilling,
          },
        },
        {
          name: 'postalCode',
          type: 'text',
          admin: {
            condition: (data, siblingData) => !siblingData.sameAsBilling,
          },
        },
        {
          name: 'country',
          type: 'text',
          defaultValue: 'India',
          admin: {
            condition: (data, siblingData) => !siblingData.sameAsBilling,
          },
        },
      ],
    },
    {
      name: 'customerType',
      type: 'select',
      options: [
        { label: 'Retail', value: 'retail' },
        { label: 'Wholesale', value: 'wholesale' },
      ],
      defaultValue: 'retail',
      required: true,
    },
    {
      name: 'status',
      type: 'select',
      options: [
        { label: 'Active', value: 'active' },
        { label: 'Inactive', value: 'inactive' },
        { label: 'Suspended', value: 'suspended' },
      ],
      defaultValue: 'active',
      required: true,
      admin: {
        description: 'Account status - only active accounts can log in',
      },
    },
    {
      name: 'creditLimit',
      type: 'number',
      min: 0,
      admin: {
        description: 'Credit limit for wholesale customers (in INR)',
        condition: (data, siblingData) => siblingData?.customerType === 'wholesale',
      },
    },
    {
      name: 'documents',
      type: 'array',
      label: 'Business Documents',
      admin: {
        description: 'Upload business documents (GST certificate, PAN card, etc.)',
        condition: (data, siblingData) => siblingData?.customerType === 'wholesale',
      },
      fields: [
        {
          name: 'name',
          type: 'text',
          required: true,
        },
        {
          name: 'document',
          type: 'upload',
          relationTo: 'media',
          required: true,
        },
      ],
    },
    {
      name: 'notes',
      type: 'textarea',
      admin: {
        position: 'sidebar',
        description: 'Internal notes about this customer (not visible to customer)',
      },
    },
    {
      name: 'initialPassword',
      type: 'text',
      admin: {
        position: 'sidebar',
        description:
          'Initial password for customer portal access (will be used to create user account)',
      },
    },
    {
      name: 'userId',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        position: 'sidebar',
        readOnly: true,
        description: 'Linked user account for portal access',
      },
    },
  ],
  hooks: {
    beforeChange: [
      async ({ data, req, operation }) => {
        // Only create user account when creating a new customer
        if (operation === 'create' && data.initialPassword) {
          try {
            // Create user account for customer
            const userDoc = await req.payload.create({
              collection: 'users',
              data: {
                email: data.email,
                password: data.initialPassword,
                name: `${data.firstName} ${data.lastName}`,
                role: 'customer',
              },
            })

            // Link the user account to the customer
            data.userId = userDoc.id

            // Remove the password from customer data for security
            delete data.initialPassword
          } catch (error) {
            req.payload.logger.error('Failed to create user account for customer:', error)
            throw new Error('Failed to create customer portal account')
          }
        }

        return data
      },
    ],
    afterChange: [
      async ({ doc, req, operation }) => {
        // Update user account when customer is created or updated
        if ((operation === 'create' || operation === 'update') && doc.userId) {
          try {
            await req.payload.update({
              collection: 'users',
              id: doc.userId,
              data: {
                email: doc.email,
                name: `${doc.firstName} ${doc.lastName}`,
                customerId: doc.id,
              },
            })
          } catch (error) {
            req.payload.logger.error('Failed to update linked user account:', error)
          }
        }
      },
    ],
  },
}

import type { CollectionConfig } from 'payload'
import { authenticated } from '../access/authenticated'

export const Orders: CollectionConfig<'orders'> = {
  slug: 'orders',
  access: {
    create: ({ req: { user } }) => <PERSON><PERSON><PERSON>(user),
    read: ({ req: { user } }) => {
      // Users can only read their own orders
      // Admins can read any order
      if (user?.collection === 'users') return true
      if (user) {
        return {
          customer: {
            equals: user.id,
          },
        }
      }
      return false
    },
    update: authenticated, // Only admins can update orders
    delete: authenticated, // Only admins can delete orders
  },
  admin: {
    useAsTitle: 'orderNumber',
    defaultColumns: ['orderNumber', 'customer', 'orderType', 'status', 'total', 'createdAt'],
  },
  fields: [
    {
      name: 'orderNumber',
      type: 'text',
      required: true,
      unique: true,
      admin: {
        description: 'Unique order number (auto-generated)',
      },
    },
    {
      name: 'customer',
      type: 'relationship',
      relationTo: 'customers',
      required: true,
    },
    {
      name: 'orderType',
      type: 'select',
      required: true,
      options: [
        { label: 'DTF Stickers', value: 'dtf-stickers' },
        { label: 'Solid T-shirts', value: 'solid-tshirts' },
        { label: 'Custom T-shirts', value: 'custom-tshirts' },
      ],
      admin: {
        description: 'Type of service for this order',
      },
    },
    {
      name: 'serviceSpecificData',
      type: 'group',
      admin: {
        description: 'Service-specific order details',
      },
      fields: [
        // DTF Stickers specific fields
        {
          name: 'dtfStickers',
          type: 'group',
          admin: {
            condition: (data) => data.orderType === 'dtf-stickers',
          },
          fields: [
            {
              name: 'designFile',
              type: 'upload',
              relationTo: 'media',
              required: true,
              admin: {
                description: 'Design file for DTF stickers (.png, .jpg, .svg, .pdf)',
              },
            },
            {
              name: 'stickerDimensions',
              type: 'group',
              fields: [
                {
                  name: 'width',
                  type: 'number',
                  required: true,
                  min: 1,
                  admin: {
                    description: 'Width in centimeters',
                  },
                },
                {
                  name: 'height',
                  type: 'number',
                  required: true,
                  min: 1,
                  admin: {
                    description: 'Height in centimeters',
                  },
                },
                {
                  name: 'isCustomSize',
                  type: 'checkbox',
                  defaultValue: false,
                },
              ],
            },
            {
              name: 'material',
              type: 'select',
              required: true,
              options: [
                { label: 'Matte', value: 'matte' },
                { label: 'Glossy', value: 'glossy' },
                { label: 'Transparent', value: 'transparent' },
                { label: 'Holographic', value: 'holographic' },
              ],
            },
            {
              name: 'finish',
              type: 'select',
              required: true,
              options: [
                { label: 'Standard', value: 'standard' },
                { label: 'Laminated', value: 'laminated' },
                { label: 'UV Resistant', value: 'uv-resistant' },
              ],
            },
            {
              name: 'quantity',
              type: 'number',
              required: true,
              min: 1,
              admin: {
                description: 'Quantity in meters',
              },
            },
            {
              name: 'pricePerUnit',
              type: 'number',
              required: true,
              min: 0,
              admin: {
                description: 'Price per meter based on quantity tier',
              },
            },
          ],
        },
        // Solid T-shirts specific fields
        {
          name: 'solidTshirts',
          type: 'array',
          admin: {
            condition: (data) => data.orderType === 'solid-tshirts',
          },
          fields: [
            {
              name: 'productVariant',
              type: 'relationship',
              relationTo: 'products',
              required: true,
            },
            {
              name: 'size',
              type: 'select',
              required: true,
              options: [
                { label: 'XS', value: 'xs' },
                { label: 'S', value: 's' },
                { label: 'M', value: 'm' },
                { label: 'L', value: 'l' },
                { label: 'XL', value: 'xl' },
                { label: 'XXL', value: 'xxl' },
                { label: 'XXXL', value: 'xxxl' },
              ],
            },
            {
              name: 'color',
              type: 'text',
              required: true,
            },
            {
              name: 'material',
              type: 'select',
              required: true,
              options: [
                { label: 'Cotton', value: 'cotton' },
                { label: 'Polyester', value: 'polyester' },
                { label: 'Poly-Cotton Blend', value: 'poly-cotton' },
                { label: 'Organic Cotton', value: 'organic' },
              ],
            },
            {
              name: 'brand',
              type: 'text',
              admin: {
                description: 'T-shirt brand/manufacturer',
              },
            },
            {
              name: 'quantity',
              type: 'number',
              required: true,
              min: 1,
            },
            {
              name: 'unitPrice',
              type: 'number',
              required: true,
              min: 0,
            },
            {
              name: 'totalPrice',
              type: 'number',
              required: true,
              min: 0,
            },
          ],
        },
        // Custom T-shirts specific fields
        {
          name: 'customTshirts',
          type: 'group',
          admin: {
            condition: (data) => data.orderType === 'custom-tshirts',
          },
          fields: [
            {
              name: 'tshirtSelection',
              type: 'group',
              fields: [
                {
                  name: 'baseProduct',
                  type: 'relationship',
                  relationTo: 'products',
                  required: true,
                  admin: {
                    description: 'Base t-shirt product',
                  },
                },
                {
                  name: 'size',
                  type: 'select',
                  required: true,
                  options: [
                    { label: 'XS', value: 'xs' },
                    { label: 'S', value: 's' },
                    { label: 'M', value: 'm' },
                    { label: 'L', value: 'l' },
                    { label: 'XL', value: 'xl' },
                    { label: 'XXL', value: 'xxl' },
                    { label: 'XXXL', value: 'xxxl' },
                  ],
                },
                {
                  name: 'color',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'material',
                  type: 'select',
                  required: true,
                  options: [
                    { label: 'Cotton', value: 'cotton' },
                    { label: 'Polyester', value: 'polyester' },
                    { label: 'Poly-Cotton Blend', value: 'poly-cotton' },
                    { label: 'Organic Cotton', value: 'organic' },
                  ],
                },
                {
                  name: 'style',
                  type: 'select',
                  options: [
                    { label: 'Crew Neck', value: 'crew-neck' },
                    { label: 'V-Neck', value: 'v-neck' },
                    { label: 'Polo', value: 'polo' },
                    { label: 'Tank Top', value: 'tank-top' },
                  ],
                  defaultValue: 'crew-neck',
                },
              ],
            },
            {
              name: 'designFile',
              type: 'upload',
              relationTo: 'media',
              required: true,
              admin: {
                description: 'Customer design file',
              },
            },
            {
              name: 'mockupImage',
              type: 'upload',
              relationTo: 'media',
              admin: {
                description: 'Generated mockup image',
              },
            },
            {
              name: 'printPlacement',
              type: 'select',
              required: true,
              options: [
                { label: 'Front', value: 'front' },
                { label: 'Back', value: 'back' },
                { label: 'Left Sleeve', value: 'left-sleeve' },
                { label: 'Right Sleeve', value: 'right-sleeve' },
                { label: 'Front & Back', value: 'front-back' },
              ],
            },
            {
              name: 'printSize',
              type: 'group',
              fields: [
                {
                  name: 'width',
                  type: 'number',
                  required: true,
                  min: 1,
                  admin: {
                    description: 'Print width in centimeters',
                  },
                },
                {
                  name: 'height',
                  type: 'number',
                  required: true,
                  min: 1,
                  admin: {
                    description: 'Print height in centimeters',
                  },
                },
              ],
            },
            {
              name: 'quantity',
              type: 'number',
              required: true,
              min: 1,
            },
            {
              name: 'advanceAmount',
              type: 'number',
              required: true,
              min: 0,
              admin: {
                description: '50% advance payment amount',
              },
            },
            {
              name: 'remainingAmount',
              type: 'number',
              required: true,
              min: 0,
              admin: {
                description: 'Remaining 50% payment amount',
              },
            },
          ],
        },
      ],
    },
    {
      name: 'subtotal',
      type: 'number',
      required: true,
      min: 0,
    },
    {
      name: 'tax',
      type: 'number',
      required: true,
      min: 0,
    },
    {
      name: 'shipping',
      type: 'number',
      required: true,
      min: 0,
    },
    {
      name: 'discount',
      type: 'number',
      min: 0,
      defaultValue: 0,
    },
    {
      name: 'total',
      type: 'number',
      required: true,
      min: 0,
    },
    {
      name: 'status',
      type: 'select',
      options: [
        { label: 'Pending', value: 'pending' },
        { label: 'Processing', value: 'processing' },
        { label: 'Partially Paid', value: 'partially_paid' },
        { label: 'Paid', value: 'paid' },
        { label: 'Shipped', value: 'shipped' },
        { label: 'Delivered', value: 'delivered' },
        { label: 'Cancelled', value: 'cancelled' },
        { label: 'Refunded', value: 'refunded' },
      ],
      required: true,
      defaultValue: 'pending',
    },
    {
      name: 'paymentMethod',
      type: 'select',
      options: [
        { label: 'Razorpay', value: 'razorpay' },
        { label: 'Bank Transfer', value: 'bank_transfer' },
        { label: 'Cash on Delivery', value: 'cod' },
      ],
    },
    {
      name: 'paymentStatus',
      type: 'select',
      options: [
        { label: 'Pending', value: 'pending' },
        { label: 'Partially Paid', value: 'partially_paid' },
        { label: 'Paid', value: 'paid' },
        { label: 'Failed', value: 'failed' },
        { label: 'Refunded', value: 'refunded' },
      ],
      required: true,
      defaultValue: 'pending',
    },
    {
      name: 'paymentTerms',
      type: 'select',
      options: [
        { label: '100% Upfront', value: 'full_upfront' },
        { label: '50% Advance, 50% on Delivery', value: 'split_payment' },
      ],
      required: true,
      admin: {
        description: 'DTF Stickers & Solid T-shirts: 100% upfront, Custom T-shirts: 50/50 split',
      },
    },
    {
      name: 'transactions',
      type: 'array',
      fields: [
        {
          name: 'transactionId',
          type: 'text',
          required: true,
        },
        {
          name: 'amount',
          type: 'number',
          required: true,
          min: 0,
        },
        {
          name: 'method',
          type: 'select',
          options: [
            { label: 'Razorpay', value: 'razorpay' },
            { label: 'Bank Transfer', value: 'bank_transfer' },
            { label: 'Cash on Delivery', value: 'cod' },
          ],
          required: true,
        },
        {
          name: 'status',
          type: 'select',
          options: [
            { label: 'Success', value: 'success' },
            { label: 'Failed', value: 'failed' },
            { label: 'Pending', value: 'pending' },
          ],
          required: true,
        },
        {
          name: 'date',
          type: 'date',
          required: true,
        },
      ],
    },
    {
      name: 'shippingDetails',
      type: 'group',
      fields: [
        {
          name: 'address',
          type: 'group',
          fields: [
            {
              name: 'line1',
              type: 'text',
              required: true,
            },
            {
              name: 'line2',
              type: 'text',
            },
            {
              name: 'city',
              type: 'text',
              required: true,
            },
            {
              name: 'state',
              type: 'text',
              required: true,
            },
            {
              name: 'postalCode',
              type: 'text',
              required: true,
            },
            {
              name: 'country',
              type: 'text',
              required: true,
              defaultValue: 'India',
            },
          ],
        },
        {
          name: 'trackingNumber',
          type: 'text',
        },
        {
          name: 'carrier',
          type: 'text',
        },
        {
          name: 'estimatedDelivery',
          type: 'date',
        },
        {
          name: 'shiprocketOrderId',
          type: 'text',
        },
      ],
    },
    {
      name: 'notes',
      type: 'textarea',
      admin: {
        description: 'Internal notes about this order (not visible to customer)',
      },
    },
    {
      name: 'customerNotes',
      type: 'textarea',
      admin: {
        description: 'Notes from the customer',
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ data }) => {
        // Generate order number if not provided
        if (data && !data.orderNumber) {
          const date = new Date()
          const timestamp = date.getTime().toString().slice(-6)
          data.orderNumber = `MT-${date.getFullYear()}${(date.getMonth() + 1)
            .toString()
            .padStart(2, '0')}${date.getDate().toString().padStart(2, '0')}-${timestamp}`
        }
        return data
      },
    ],
  },
}

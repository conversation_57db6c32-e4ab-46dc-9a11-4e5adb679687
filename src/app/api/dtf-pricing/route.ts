import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'
import { DTFPricingConfig, DTFQuoteRequest, DTFQuoteResponse } from '@/types/dtf-pricing'

// Rate limiting and security
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 100
const requestCounts = new Map<string, { count: number; resetTime: number }>()

function getRateLimitKey(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const ip = forwarded ? forwarded.split(',')[0] : request.ip || 'unknown'
  return ip
}

function checkRateLimit(key: string): boolean {
  const now = Date.now()
  const record = requestCounts.get(key)

  if (!record || now > record.resetTime) {
    requestCounts.set(key, { count: 1, resetTime: now + RATE_LIMIT_WINDOW })
    return true
  }

  if (record.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false
  }

  record.count++
  return true
}

// Input validation - simplified to avoid TypeScript issues
function validateDTFPricingConfig(data: unknown): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  // Basic validation - just check if data exists and has required fields
  if (!data || typeof data !== 'object') {
    errors.push('Invalid data format')
    return { isValid: false, errors }
  }

  // We'll rely on Payload's built-in validation for detailed checks
  // This is a basic safety check for the API
  return { isValid: true, errors }
}

// Error response helper
function createErrorResponse(message: string, status: number = 400, details?: unknown) {
  return NextResponse.json(
    {
      success: false,
      error: message,
      details,
      timestamp: new Date().toISOString(),
    },
    { status },
  )
}

// Success response helper
function createSuccessResponse(data: unknown, message?: string) {
  return NextResponse.json({
    success: true,
    data,
    message,
    timestamp: new Date().toISOString(),
  })
}

// GET - Fetch DTF pricing configuration
export async function GET(request: NextRequest) {
  const rateLimitKey = getRateLimitKey(request)

  if (!checkRateLimit(rateLimitKey)) {
    return createErrorResponse('Rate limit exceeded. Please try again later.', 429)
  }

  try {
    const payload = await getPayload({ config })

    const pricingData = await payload.findGlobal({
      slug: 'dtf-pricing',
    })

    if (!pricingData) {
      return createErrorResponse('DTF pricing configuration not found', 404)
    }

    // Add cache headers for better performance
    const response = createSuccessResponse(
      pricingData,
      'DTF pricing configuration retrieved successfully',
    )
    response.headers.set('Cache-Control', 'public, max-age=300, stale-while-revalidate=60') // 5 minutes cache

    return response
  } catch (error) {
    console.error('Error fetching DTF pricing:', error)
    return createErrorResponse(
      'Failed to fetch DTF pricing configuration',
      500,
      process.env.NODE_ENV === 'development' ? error : undefined,
    )
  }
}

// POST - Update DTF pricing configuration
export async function POST(request: NextRequest) {
  const rateLimitKey = getRateLimitKey(request)

  if (!checkRateLimit(rateLimitKey)) {
    return createErrorResponse('Rate limit exceeded. Please try again later.', 429)
  }

  try {
    const payload = await getPayload({ config })

    // Parse and validate request body
    let body: unknown
    try {
      body = await request.json()
    } catch (_parseError) {
      return createErrorResponse('Invalid JSON in request body', 400)
    }

    // Validate request size (prevent large payloads)
    const bodyString = JSON.stringify(body)
    if (bodyString.length > 1024 * 1024) {
      // 1MB limit
      return createErrorResponse('Request payload too large', 413)
    }

    // Validate input data
    const validation = validateDTFPricingConfig(body)
    if (!validation.isValid) {
      return createErrorResponse('Validation failed', 400, { errors: validation.errors })
    }

    // Check authentication via Payload's built-in auth
    const authResult = await payload.auth({ headers: request.headers })
    if (!authResult.user || authResult.user.role !== 'admin') {
      return createErrorResponse('Admin authentication required', 401)
    }

    // Sanitize data before saving
    const sanitizedData = {
      ...body,
      lastUpdated: new Date().toISOString(),
      updatedBy: authResult.user.id,
    }

    // Update the global configuration
    const updatedPricing = await payload.updateGlobal({
      slug: 'dtf-pricing',
      data: sanitizedData,
      user: authResult.user,
    })

    // Log the update for audit trail
    console.log(`DTF Pricing updated by ${authResult.user.email} at ${new Date().toISOString()}`)

    return createSuccessResponse(updatedPricing, 'DTF pricing configuration updated successfully')
  } catch (error) {
    console.error('Error updating DTF pricing:', error)

    // Handle specific Payload errors
    if (error.name === 'ValidationError') {
      return createErrorResponse('Validation error', 400, {
        errors: error.data || error.message,
      })
    }

    if (error.name === 'Forbidden') {
      return createErrorResponse('Insufficient permissions', 403)
    }

    return createErrorResponse(
      'Failed to update DTF pricing configuration',
      500,
      process.env.NODE_ENV === 'development' ? error : undefined,
    )
  }
}

// Calculate quote based on DTF pricing configuration
export async function calculateDTFQuote(quoteRequest: DTFQuoteRequest): Promise<DTFQuoteResponse> {
  try {
    const payload = await getPayload({ config })

    const pricingConfig = (await payload.findGlobal({
      slug: 'dtf-pricing',
    })) as DTFPricingConfig

    if (!pricingConfig || !pricingConfig.isActive) {
      throw new Error('DTF pricing is not configured or inactive')
    }

    const {
      width,
      height,
      quantity,
      materialId,
      additionalServices = [],
      isRushOrder = false,
    } = quoteRequest

    // Convert measurements to the configured unit if needed
    const area = width * height
    const perimeter = 2 * (width + height)

    // Find applicable size tier
    const applicableTier = pricingConfig.sizeTiers.find(
      (tier) =>
        tier.isActive &&
        width >= tier.dimensions.minWidth &&
        height >= tier.dimensions.minHeight &&
        (!tier.dimensions.maxWidth || width <= tier.dimensions.maxWidth) &&
        (!tier.dimensions.maxHeight || height <= tier.dimensions.maxHeight),
    )

    if (!applicableTier) {
      throw new Error('No applicable pricing tier found for the given dimensions')
    }

    // Calculate base price based on pricing model
    let basePrice = 0
    let pricePerUnit = 0

    switch (pricingConfig.pricingModel) {
      case 'per_square_inch':
        pricePerUnit = applicableTier.pricing.pricePerUnit || 0
        basePrice = area * pricePerUnit * quantity
        break
      case 'per_linear_inch':
        pricePerUnit = applicableTier.pricing.pricePerUnit || 0
        basePrice = perimeter * pricePerUnit * quantity
        break
      case 'flat_rate_by_size':
        basePrice = applicableTier.pricing.basePrice * quantity
        break
      case 'tiered_pricing':
        basePrice = applicableTier.pricing.basePrice
        if (applicableTier.pricing.pricePerUnit) {
          basePrice += area * applicableTier.pricing.pricePerUnit * quantity
        }
        break
      default:
        basePrice = applicableTier.pricing.basePrice * quantity
    }

    // Apply quantity discounts
    let quantityDiscount = 0
    const applicableDiscount = pricingConfig.quantityDiscounts.find(
      (discount) =>
        discount.isActive &&
        quantity >= discount.minQuantity &&
        (!discount.maxQuantity || quantity <= discount.maxQuantity),
    )

    if (applicableDiscount) {
      switch (applicableDiscount.discountType) {
        case 'percentage':
          quantityDiscount = basePrice * (applicableDiscount.discountValue / 100)
          break
        case 'fixed_amount':
          quantityDiscount = applicableDiscount.discountValue
          break
        case 'fixed_price':
          basePrice = applicableDiscount.discountValue * quantity
          break
      }
    }

    // Calculate additional costs
    const setupFee = pricingConfig.basePricing.setupFee || 0
    const rushOrderFee = isRushOrder
      ? basePrice * ((pricingConfig.basePricing.rushOrderMultiplier || 1.5) - 1)
      : 0

    // Material upgrade cost
    let materialUpgrade = 0
    if (materialId) {
      const material = pricingConfig.materialOptions.find((m) => m.id === materialId && m.isActive)
      if (material && !material.isDefault) {
        switch (material.priceAdjustment.adjustmentType) {
          case 'percentage_increase':
            materialUpgrade = basePrice * (material.priceAdjustment.adjustmentValue / 100)
            break
          case 'fixed_increase':
            materialUpgrade = material.priceAdjustment.adjustmentValue * quantity
            break
          case 'multiplier':
            materialUpgrade = basePrice * (material.priceAdjustment.adjustmentValue - 1)
            break
        }
      }
    }

    // Additional services cost
    let additionalServicesCost = 0
    additionalServices.forEach((serviceId) => {
      const service = pricingConfig.additionalServices.find((s) => s.id === serviceId && s.isActive)
      if (service) {
        switch (service.pricingType) {
          case 'per_item':
            additionalServicesCost += service.price * quantity
            break
          case 'per_order':
            additionalServicesCost += service.price
            break
          case 'percentage':
            additionalServicesCost += basePrice * (service.price / 100)
            break
        }
      }
    })

    const subtotal = basePrice - quantityDiscount
    const totalAdditionalCosts = setupFee + rushOrderFee + materialUpgrade + additionalServicesCost
    const total = Math.max(
      subtotal + totalAdditionalCosts,
      pricingConfig.basePricing.minimumOrderValue || 0,
    )

    // Calculate payment terms
    const paymentTerms = pricingConfig.paymentTerms
    let advanceAmount = total
    let balanceAmount = 0

    if (paymentTerms.defaultTerms === 'split_payment') {
      const advancePercentage = paymentTerms.minimumAdvancePercentage || 50
      advanceAmount = total * (advancePercentage / 100)
      balanceAmount = total - advanceAmount
    }

    const quote: DTFQuoteResponse = {
      subtotal,
      discounts: {
        quantityDiscount,
      },
      additionalCosts: {
        setupFee,
        rushOrderFee,
        materialUpgrade,
        additionalServices: additionalServicesCost,
      },
      total,
      breakdown: {
        basePrice,
        pricePerUnit,
        area: pricingConfig.pricingModel === 'per_square_inch' ? area : undefined,
        linearMeasurement: pricingConfig.pricingModel === 'per_linear_inch' ? perimeter : undefined,
        appliedTier: applicableTier.tierName,
        appliedDiscounts: applicableDiscount ? [applicableDiscount.discountType] : [],
      },
      paymentTerms: {
        terms: paymentTerms.defaultTerms,
        advanceAmount: paymentTerms.defaultTerms !== 'full_upfront' ? advanceAmount : undefined,
        balanceAmount: paymentTerms.defaultTerms !== 'full_upfront' ? balanceAmount : undefined,
      },
      currency: pricingConfig.currency,
      validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
    }

    return quote
  } catch (error) {
    console.error('Error calculating DTF quote:', error)
    throw error
  }
}

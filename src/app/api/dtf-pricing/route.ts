import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

// Rate limiting and security
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 100
const requestCounts = new Map<string, { count: number; resetTime: number }>()

function getRateLimitKey(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  if (forwarded) {
    const ip = forwarded.split(',')[0]?.trim()
    return ip || 'unknown'
  }
  return 'unknown'
}

function checkRateLimit(key: string): boolean {
  const now = Date.now()
  const record = requestCounts.get(key)

  if (!record || now > record.resetTime) {
    requestCounts.set(key, { count: 1, resetTime: now + RATE_LIMIT_WINDOW })
    return true
  }

  if (record.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false
  }

  record.count++
  return true
}

// Input validation - simplified to avoid TypeScript issues
function validateDTFPricingConfig(data: unknown): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  // Basic validation - just check if data exists and has required fields
  if (!data || typeof data !== 'object') {
    errors.push('Invalid data format')
    return { isValid: false, errors }
  }

  // We'll rely on Payload's built-in validation for detailed checks
  // This is a basic safety check for the API
  return { isValid: true, errors }
}

// Error response helper
function createErrorResponse(message: string, status: number = 400, details?: unknown) {
  return NextResponse.json(
    {
      success: false,
      error: message,
      details,
      timestamp: new Date().toISOString(),
    },
    { status },
  )
}

// Success response helper
function createSuccessResponse(data: unknown, message?: string) {
  return NextResponse.json({
    success: true,
    data,
    message,
    timestamp: new Date().toISOString(),
  })
}

// GET - Fetch DTF pricing configuration
export async function GET(request: NextRequest) {
  const rateLimitKey = getRateLimitKey(request)

  if (!checkRateLimit(rateLimitKey)) {
    return createErrorResponse('Rate limit exceeded. Please try again later.', 429)
  }

  try {
    const payload = await getPayload({ config })

    const pricingData = await payload.findGlobal({
      slug: 'dtf-pricing',
    })

    if (!pricingData) {
      return createErrorResponse('DTF pricing configuration not found', 404)
    }

    // Add cache headers for better performance
    const response = createSuccessResponse(
      pricingData,
      'DTF pricing configuration retrieved successfully',
    )
    response.headers.set('Cache-Control', 'public, max-age=300, stale-while-revalidate=60') // 5 minutes cache

    return response
  } catch (error) {
    console.error('Error fetching DTF pricing:', error)
    return createErrorResponse(
      'Failed to fetch DTF pricing configuration',
      500,
      process.env.NODE_ENV === 'development' ? error : undefined,
    )
  }
}

// POST - Update DTF pricing configuration
export async function POST(request: NextRequest) {
  const rateLimitKey = getRateLimitKey(request)

  if (!checkRateLimit(rateLimitKey)) {
    return createErrorResponse('Rate limit exceeded. Please try again later.', 429)
  }

  try {
    const payload = await getPayload({ config })

    // Parse and validate request body
    let body: unknown
    try {
      body = await request.json()
    } catch (_parseError) {
      return createErrorResponse('Invalid JSON in request body', 400)
    }

    // Validate request size (prevent large payloads)
    const bodyString = JSON.stringify(body)
    if (bodyString.length > 1024 * 1024) {
      // 1MB limit
      return createErrorResponse('Request payload too large', 413)
    }

    // Validate input data
    const validation = validateDTFPricingConfig(body)
    if (!validation.isValid) {
      return createErrorResponse('Validation failed', 400, { errors: validation.errors })
    }

    // Check authentication via Payload's built-in auth
    const authResult = await payload.auth({ headers: request.headers })
    if (!authResult.user || authResult.user.role !== 'admin') {
      return createErrorResponse('Admin authentication required', 401)
    }

    // Sanitize data before saving
    const sanitizedData = {
      ...body,
      lastUpdated: new Date().toISOString(),
      updatedBy: authResult.user.id,
    }

    // Update the global configuration
    const updatedPricing = await payload.updateGlobal({
      slug: 'dtf-pricing',
      data: sanitizedData,
      user: authResult.user,
    })

    // Log the update for audit trail
    console.log(`DTF Pricing updated by ${authResult.user.email} at ${new Date().toISOString()}`)

    return createSuccessResponse(updatedPricing, 'DTF pricing configuration updated successfully')
  } catch (error) {
    console.error('Error updating DTF pricing:', error)

    // Handle specific Payload errors
    if (error.name === 'ValidationError') {
      return createErrorResponse('Validation error', 400, {
        errors: error.data || error.message,
      })
    }

    if (error.name === 'Forbidden') {
      return createErrorResponse('Insufficient permissions', 403)
    }

    return createErrorResponse(
      'Failed to update DTF pricing configuration',
      500,
      process.env.NODE_ENV === 'development' ? error : undefined,
    )
  }
}

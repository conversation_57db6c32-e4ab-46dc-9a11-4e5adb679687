import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'
import { DTFPricingConfig, DTFQuoteRequest, DTFQuoteResponse } from '@/types/dtf-pricing'

// Rate limiting
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 50 // Lower limit for quote calculations
const requestCounts = new Map<string, { count: number; resetTime: number }>()

function getRateLimitKey(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const ip = forwarded ? forwarded.split(',')[0] : request.ip || 'unknown'
  return ip
}

function checkRateLimit(key: string): boolean {
  const now = Date.now()
  const record = requestCounts.get(key)

  if (!record || now > record.resetTime) {
    requestCounts.set(key, { count: 1, resetTime: now + RATE_LIMIT_WINDOW })
    return true
  }

  if (record.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false
  }

  record.count++
  return true
}

// Validation for quote requests
function validateQuoteRequest(data: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  if (typeof data.width !== 'number' || data.width <= 0) {
    errors.push('Width must be a positive number')
  }

  if (typeof data.height !== 'number' || data.height <= 0) {
    errors.push('Height must be a positive number')
  }

  if (typeof data.quantity !== 'number' || data.quantity <= 0 || !Number.isInteger(data.quantity)) {
    errors.push('Quantity must be a positive integer')
  }

  if (data.width && data.width > 10000) {
    errors.push('Width cannot exceed 10,000 units')
  }

  if (data.height && data.height > 10000) {
    errors.push('Height cannot exceed 10,000 units')
  }

  if (data.quantity && data.quantity > 100000) {
    errors.push('Quantity cannot exceed 100,000 items')
  }

  if (data.measurementUnit && !['inches', 'centimeters', 'millimeters'].includes(data.measurementUnit)) {
    errors.push('Invalid measurement unit')
  }

  if (data.materialId && typeof data.materialId !== 'string') {
    errors.push('Material ID must be a string')
  }

  if (data.additionalServices && !Array.isArray(data.additionalServices)) {
    errors.push('Additional services must be an array')
  }

  if (data.isRushOrder && typeof data.isRushOrder !== 'boolean') {
    errors.push('Rush order flag must be a boolean')
  }

  return { isValid: errors.length === 0, errors }
}

// Error response helper
function createErrorResponse(message: string, status: number = 400, details?: any) {
  return NextResponse.json(
    {
      success: false,
      error: message,
      details,
      timestamp: new Date().toISOString(),
    },
    { status }
  )
}

// Success response helper
function createSuccessResponse(data: any, message?: string) {
  return NextResponse.json({
    success: true,
    data,
    message,
    timestamp: new Date().toISOString(),
  })
}

// POST - Calculate DTF printing quote
export async function POST(request: NextRequest) {
  const rateLimitKey = getRateLimitKey(request)
  
  if (!checkRateLimit(rateLimitKey)) {
    return createErrorResponse('Rate limit exceeded. Please try again later.', 429)
  }

  try {
    const payload = await getPayload({ config })
    
    // Parse and validate request body
    let quoteRequest: DTFQuoteRequest
    try {
      quoteRequest = await request.json()
    } catch (parseError) {
      return createErrorResponse('Invalid JSON in request body', 400)
    }

    // Validate input data
    const validation = validateQuoteRequest(quoteRequest)
    if (!validation.isValid) {
      return createErrorResponse('Validation failed', 400, { errors: validation.errors })
    }

    // Get pricing configuration
    const pricingConfig = await payload.findGlobal({
      slug: 'dtf-pricing',
    }) as DTFPricingConfig

    if (!pricingConfig || !pricingConfig.isActive) {
      return createErrorResponse('DTF pricing is not configured or inactive', 503)
    }

    // Calculate quote
    const quote = await calculateDTFQuote(pricingConfig, quoteRequest)

    // Add cache headers for quote calculations
    const response = createSuccessResponse(quote, 'Quote calculated successfully')
    response.headers.set('Cache-Control', 'private, max-age=60') // 1 minute cache for quotes
    
    return response
  } catch (error) {
    console.error('Error calculating DTF quote:', error)
    
    if (error.message.includes('No applicable pricing tier')) {
      return createErrorResponse('No pricing tier available for the specified dimensions', 400)
    }

    return createErrorResponse(
      'Failed to calculate quote',
      500,
      process.env.NODE_ENV === 'development' ? error : undefined
    )
  }
}

// Calculate quote based on DTF pricing configuration
async function calculateDTFQuote(pricingConfig: DTFPricingConfig, quoteRequest: DTFQuoteRequest): Promise<DTFQuoteResponse> {
  const {
    width,
    height,
    quantity,
    materialId,
    additionalServices = [],
    isRushOrder = false,
  } = quoteRequest

  // Convert measurements to the configured unit if needed
  const area = width * height
  const perimeter = 2 * (width + height)

  // Find applicable size tier
  const applicableTier = pricingConfig.sizeTiers.find(
    (tier) =>
      tier.isActive &&
      width >= tier.dimensions.minWidth &&
      height >= tier.dimensions.minHeight &&
      (!tier.dimensions.maxWidth || width <= tier.dimensions.maxWidth) &&
      (!tier.dimensions.maxHeight || height <= tier.dimensions.maxHeight),
  )

  if (!applicableTier) {
    throw new Error('No applicable pricing tier found for the given dimensions')
  }

  // Calculate base price based on pricing model
  let basePrice = 0
  let pricePerUnit = 0

  switch (pricingConfig.pricingModel) {
    case 'per_square_inch':
      pricePerUnit = applicableTier.pricing.pricePerUnit || 0
      basePrice = area * pricePerUnit * quantity
      break
    case 'per_linear_inch':
      pricePerUnit = applicableTier.pricing.pricePerUnit || 0
      basePrice = perimeter * pricePerUnit * quantity
      break
    case 'flat_rate_by_size':
      basePrice = applicableTier.pricing.basePrice * quantity
      break
    case 'tiered_pricing':
      basePrice = applicableTier.pricing.basePrice
      if (applicableTier.pricing.pricePerUnit) {
        basePrice += area * applicableTier.pricing.pricePerUnit * quantity
      }
      break
    default:
      basePrice = applicableTier.pricing.basePrice * quantity
  }

  // Apply quantity discounts
  let quantityDiscount = 0
  const applicableDiscount = pricingConfig.quantityDiscounts?.find(
    (discount) =>
      discount.isActive &&
      quantity >= discount.minQuantity &&
      (!discount.maxQuantity || quantity <= discount.maxQuantity),
  )

  if (applicableDiscount) {
    switch (applicableDiscount.discountType) {
      case 'percentage':
        quantityDiscount = basePrice * (applicableDiscount.discountValue / 100)
        break
      case 'fixed_amount':
        quantityDiscount = applicableDiscount.discountValue
        break
      case 'fixed_price':
        basePrice = applicableDiscount.discountValue * quantity
        break
    }
  }

  // Calculate additional costs
  const setupFee = pricingConfig.basePricing.setupFee || 0
  const rushOrderFee = isRushOrder
    ? basePrice * ((pricingConfig.basePricing.rushOrderMultiplier || 1.5) - 1)
    : 0

  // Material upgrade cost
  let materialUpgrade = 0
  if (materialId) {
    const material = pricingConfig.materialOptions?.find((m) => m.id === materialId && m.isActive)
    if (material && !material.isDefault) {
      switch (material.priceAdjustment.adjustmentType) {
        case 'percentage_increase':
          materialUpgrade = basePrice * (material.priceAdjustment.adjustmentValue / 100)
          break
        case 'fixed_increase':
          materialUpgrade = material.priceAdjustment.adjustmentValue * quantity
          break
        case 'multiplier':
          materialUpgrade = basePrice * (material.priceAdjustment.adjustmentValue - 1)
          break
      }
    }
  }

  // Additional services cost
  let additionalServicesCost = 0
  additionalServices.forEach((serviceId) => {
    const service = pricingConfig.additionalServices?.find((s) => s.id === serviceId && s.isActive)
    if (service) {
      switch (service.pricingType) {
        case 'per_item':
          additionalServicesCost += service.price * quantity
          break
        case 'per_order':
          additionalServicesCost += service.price
          break
        case 'percentage':
          additionalServicesCost += basePrice * (service.price / 100)
          break
      }
    }
  })

  const subtotal = basePrice - quantityDiscount
  const totalAdditionalCosts = setupFee + rushOrderFee + materialUpgrade + additionalServicesCost
  const total = Math.max(
    subtotal + totalAdditionalCosts,
    pricingConfig.basePricing.minimumOrderValue || 0,
  )

  // Calculate payment terms
  const paymentTerms = pricingConfig.paymentTerms
  let advanceAmount = total
  let balanceAmount = 0

  if (paymentTerms.defaultTerms === 'split_payment') {
    const advancePercentage = paymentTerms.minimumAdvancePercentage || 50
    advanceAmount = total * (advancePercentage / 100)
    balanceAmount = total - advanceAmount
  }

  const quote: DTFQuoteResponse = {
    subtotal,
    discounts: {
      quantityDiscount,
    },
    additionalCosts: {
      setupFee,
      rushOrderFee,
      materialUpgrade,
      additionalServices: additionalServicesCost,
    },
    total,
    breakdown: {
      basePrice,
      pricePerUnit,
      area: pricingConfig.pricingModel === 'per_square_inch' ? area : undefined,
      linearMeasurement: pricingConfig.pricingModel === 'per_linear_inch' ? perimeter : undefined,
      appliedTier: applicableTier.tierName,
      appliedDiscounts: applicableDiscount ? [applicableDiscount.discountType] : [],
    },
    paymentTerms: {
      terms: paymentTerms.defaultTerms,
      advanceAmount: paymentTerms.defaultTerms !== 'full_upfront' ? advanceAmount : undefined,
      balanceAmount: paymentTerms.defaultTerms !== 'full_upfront' ? balanceAmount : undefined,
    },
    currency: pricingConfig.currency,
    validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
  }

  return quote
}

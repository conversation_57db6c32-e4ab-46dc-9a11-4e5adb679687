import { NextRequest, NextResponse } from 'next/server'
import { DTFQuoteRequest } from '@/types/dtf-pricing'
import { calculateDTFQuote } from '@/utilities/dtf-quote-calculator'

// Rate limiting
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 50 // Lower limit for quote calculations
const requestCounts = new Map<string, { count: number; resetTime: number }>()

function getRateLimitKey(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  if (forwarded) {
    const ip = forwarded.split(',')[0]?.trim()
    return ip || 'unknown'
  }
  return 'unknown'
}

function checkRateLimit(key: string): boolean {
  const now = Date.now()
  const record = requestCounts.get(key)

  if (!record || now > record.resetTime) {
    requestCounts.set(key, { count: 1, resetTime: now + RATE_LIMIT_WINDOW })
    return true
  }

  if (record.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false
  }

  record.count++
  return true
}

// Validation for quote requests
function validateQuoteRequest(data: unknown): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  // Basic validation - just check if data exists
  if (!data || typeof data !== 'object') {
    errors.push('Invalid data format')
    return { isValid: false, errors }
  }

  // We'll rely on runtime checks in the calculation function
  return { isValid: true, errors }
}

// Error response helper
function createErrorResponse(message: string, status: number = 400, details?: unknown) {
  return NextResponse.json(
    {
      success: false,
      error: message,
      details,
      timestamp: new Date().toISOString(),
    },
    { status },
  )
}

// Success response helper
function createSuccessResponse(data: unknown, message?: string) {
  return NextResponse.json({
    success: true,
    data,
    message,
    timestamp: new Date().toISOString(),
  })
}

// POST - Calculate DTF printing quote
export async function POST(request: NextRequest) {
  const rateLimitKey = getRateLimitKey(request)

  if (!checkRateLimit(rateLimitKey)) {
    return createErrorResponse('Rate limit exceeded. Please try again later.', 429)
  }

  try {
    // Parse and validate request body
    let quoteRequest: DTFQuoteRequest
    try {
      quoteRequest = await request.json()
    } catch (_parseError) {
      return createErrorResponse('Invalid JSON in request body', 400)
    }

    // Validate input data
    const validation = validateQuoteRequest(quoteRequest)
    if (!validation.isValid) {
      return createErrorResponse('Validation failed', 400, { errors: validation.errors })
    }

    // Calculate quote using utility function
    const quote = await calculateDTFQuote(quoteRequest)

    // Add cache headers for quote calculations
    const response = createSuccessResponse(quote, 'Quote calculated successfully')
    response.headers.set('Cache-Control', 'private, max-age=60') // 1 minute cache for quotes

    return response
  } catch (error) {
    console.error('Error calculating DTF quote:', error)

    if (error.message.includes('No applicable pricing tier')) {
      return createErrorResponse('No pricing tier available for the specified dimensions', 400)
    }

    return createErrorResponse(
      'Failed to calculate quote',
      500,
      process.env.NODE_ENV === 'development' ? error : undefined,
    )
  }
}

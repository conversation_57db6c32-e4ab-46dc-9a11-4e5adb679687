/**
 * Test script to verify customer portal functionality
 * This script tests the customer user creation and access controls
 */

import { getPayload } from 'payload'
import config from '@payload-config'

async function testCustomerPortal() {
  const payload = await getPayload({ config })

  console.log('🧪 Testing Customer Portal Implementation...\n')

  try {
    // Test 1: Create a test customer with user account
    console.log('1. Creating test customer with user account...')
    const testCustomer = await payload.create({
      collection: 'customers',
      data: {
        companyName: 'Test Company Ltd',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        phone: '+91-**********',
        billingAddress: {
          line1: '123 Test Street',
          city: 'Mumbai',
          state: 'Maharashtra',
          postalCode: '400001',
          country: 'India',
        },
        customerType: 'retail',
        status: 'active',
        initialPassword: 'TestPassword123!',
      },
    })

    console.log('✅ Customer created successfully:', testCustomer.email)
    console.log('✅ User account linked:', testCustomer.userId ? 'Yes' : 'No')

    // Test 2: Verify user account was created
    if (testCustomer.userId) {
      const linkedUser = await payload.findByID({
        collection: 'users',
        id: typeof testCustomer.userId === 'string' ? testCustomer.userId : testCustomer.userId.id,
      })

      console.log('✅ Linked user account found:', linkedUser.email)
      console.log('✅ User role:', linkedUser.role)
      console.log('✅ Customer ID linked:', linkedUser.customerId ? 'Yes' : 'No')
    }

    // Test 3: Create a test order for the customer
    console.log('\n2. Creating test order for customer...')
    const testOrder = await payload.create({
      collection: 'orders',
      data: {
        orderNumber: 'TEST-001',
        customer: testCustomer.id,
        orderType: 'dtf-stickers',
        status: 'pending',
        total: 1500,
        serviceSpecificData: {
          dtfStickers: {
            designFile: 'placeholder-media-id', // Would be a media ID in real scenario
            stickerDimensions: {
              width: 10,
              height: 10,
              isCustomSize: false,
            },
            material: 'matte',
            quantity: 100,
            unitPrice: 15,
            totalPrice: 1500,
          },
        },
        paymentTerms: 'advance',
        shippingDetails: {
          address: {
            line1: '123 Test Street',
            city: 'Mumbai',
            state: 'Maharashtra',
            postalCode: '400001',
            country: 'India',
          },
        },
      },
    })

    console.log('✅ Test order created:', testOrder.orderNumber)

    // Test 4: Test access controls
    console.log('\n3. Testing access controls...')

    // Simulate customer user login and test order access
    const customerUser = await payload.findByID({
      collection: 'users',
      id: typeof testCustomer.userId === 'string' ? testCustomer.userId : testCustomer.userId.id,
    })

    // Test customer can access their own orders
    const customerOrders = await payload.find({
      collection: 'orders',
      user: customerUser,
    })

    console.log('✅ Customer can access orders:', customerOrders.docs.length > 0 ? 'Yes' : 'No')
    console.log('✅ Number of orders accessible to customer:', customerOrders.docs.length)

    console.log('\n🎉 All tests passed! Customer portal implementation is working correctly.')

    // Cleanup
    console.log('\n4. Cleaning up test data...')
    await payload.delete({
      collection: 'orders',
      id: testOrder.id,
    })
    await payload.delete({
      collection: 'users',
      id: typeof testCustomer.userId === 'string' ? testCustomer.userId : testCustomer.userId.id,
    })
    await payload.delete({
      collection: 'customers',
      id: testCustomer.id,
    })
    console.log('✅ Test data cleaned up')
  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  testCustomerPortal()
    .then(() => {
      console.log('\n✨ Test completed')
      process.exit(0)
    })
    .catch((error) => {
      console.error('❌ Test script failed:', error)
      process.exit(1)
    })
}

export { testCustomerPortal }
